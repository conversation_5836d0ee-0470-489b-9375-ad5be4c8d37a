/*
=======================================================
= File: gmx_online_mode.dart
= Project: LavaMail
= Description:
=   - GMX API service for online mode using IMAP/SMTP
=   - Implements email operations through direct IMAP/SMTP connection
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:enough_mail/enough_mail.dart';
import '../models/gmx_user.dart';
import 'gmx_imap_client.dart';

// =======================================================
// = Class: GmxApiService
// = Description: GMX API service using IMAP/SMTP
// =======================================================
class GmxApiService {
  final GmxUser? user;
  final Logger _logger = Logger();
  late GmxImapClient _imapClient;
  bool _initialized = false;

  GmxApiService(this.user);

  // =======================================================
  // = Function: _ensureInitialized
  // = Description: Ensures the service is initialized
  // =======================================================
  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      if (user == null) {
        throw Exception('No GMX user provided');
      }
      _imapClient = GmxImapClient();
      await _imapClient.connect();
      _initialized = true;
      _logger.i('GMX API service initialized for: ${user!.email}');
    }
  }

  // =======================================================
  // = Function: getCategoryStats
  // = Description: Gets statistics for email categories with real GMX folder names
  // =======================================================
  Future<Map<String, dynamic>> getCategoryStats(String category) async {
    try {
      await _ensureInitialized();
      _logger.d('Getting GMX stats for category: $category');
      final mailboxName = _resolveGmxFolderName(category);
      final stats = await _getMailboxStats(mailboxName);
      _logger.d('GMX stats for $category ($mailboxName): $stats');
      return stats;
    } catch (e, stack) {
      _logger.e('Error getting GMX category stats for $category', error: e, stackTrace: stack);
      return {
        'count': 0,
        'unread': 0,
        'size': 0,
      };
    }
  }

  // =======================================================
  // = Function: getAllFolders
  // = Description: Gets all available folders with their statistics using comprehensive discovery
  // =======================================================
  Future<List<Map<String, dynamic>>> getAllFolders() async {
    try {
      await _ensureInitialized();
      _logger.d('Getting all GMX folders with comprehensive discovery');

      // Use the new comprehensive folder discovery method
      final discoveredFolders = await _imapClient.discoverFolderStructure();
      final folders = <Map<String, dynamic>>[];

      for (final folderInfo in discoveredFolders) {
        folders.add({
          'id': folderInfo['name'],
          'name': folderInfo['name'],
          'displayName': folderInfo['displayName'],
          'path': folderInfo['path'],
          'count': folderInfo['messageCount'] ?? 0,
          'unread': folderInfo['unreadCount'] ?? 0,
          'size': 0, // GMX doesn't provide size info easily via IMAP
          'isSelectable': folderInfo['isSelectable'] ?? false,
          'hasChildren': folderInfo['hasChildren'] ?? false,
          'flags': folderInfo['flags'] ?? <String>[],
          'separator': folderInfo['separator'],
        });
      }

      _logger.i('Found ${folders.length} GMX folders using comprehensive discovery');
      return folders;
    } catch (e, stack) {
      _logger.e('Error getting GMX folders with discovery', error: e, stackTrace: stack);

      // Fallback to basic method if comprehensive discovery fails
      _logger.w('Falling back to basic folder discovery');
      return await _getAllFoldersBasic();
    }
  }

  // =======================================================
  // = Function: _getAllFoldersBasic
  // = Description: Fallback method for basic folder discovery
  // =======================================================
  Future<List<Map<String, dynamic>>> _getAllFoldersBasic() async {
    try {
      final mailboxes = await _imapClient.getMailboxes();
      final folders = <Map<String, dynamic>>[];

      for (final mailbox in mailboxes) {
        try {
          final stats = await _getMailboxStats(mailbox.name);
          folders.add({
            'id': mailbox.name,
            'name': mailbox.name,
            'displayName': _getFolderDisplayName(mailbox.name),
            'path': mailbox.path,
            'count': stats['count'],
            'unread': stats['unread'],
            'size': stats['size'],
            'isSelectable': true,
            'hasChildren': mailbox.hasChildren,
          });
        } catch (e) {
          _logger.w('Error getting stats for ${mailbox.name}: $e');
          folders.add({
            'id': mailbox.name,
            'name': mailbox.name,
            'displayName': _getFolderDisplayName(mailbox.name),
            'path': mailbox.path,
            'count': 0,
            'unread': 0,
            'size': 0,
            'isSelectable': false,
            'hasChildren': mailbox.hasChildren,
          });
        }
      }

      return folders;
    } catch (e, stack) {
      _logger.e('Error in basic folder discovery', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: discoverAndMapGmxStructure
  // = Description: Discovers GMX structure and maps it to standard categories
  // = Inspired by the Python gmx.py comprehensive analysis
  // =======================================================
  Future<Map<String, dynamic>> discoverAndMapGmxStructure() async {
    try {
      await _ensureInitialized();
      _logger.d('Discovering and mapping GMX folder structure');

      final discoveredFolders = await _imapClient.discoverFolderStructure();

      // Map folders to standard categories (comprehensive GMX structure)
      final structureMap = <String, dynamic>{
        'inbox': <Map<String, dynamic>>[],
        'sent': <Map<String, dynamic>>[],
        'spam': <Map<String, dynamic>>[],
        'trash': <Map<String, dynamic>>[],
        'drafts': <Map<String, dynamic>>[],
        'archive': <Map<String, dynamic>>[],
        'important': <Map<String, dynamic>>[],
        'templates': <Map<String, dynamic>>[],
        'notes': <Map<String, dynamic>>[],
        'calendar': <Map<String, dynamic>>[],
        'contacts': <Map<String, dynamic>>[],
        'outbox': <Map<String, dynamic>>[],
        'custom': <Map<String, dynamic>>[],
        'totalFolders': discoveredFolders.length,
        'totalMessages': 0,
        'totalUnread': 0,
      };

      int totalMessages = 0;
      int totalUnread = 0;

      for (final folder in discoveredFolders) {
        final folderName = (folder['name'] as String).toLowerCase();
        final messageCount = folder['messageCount'] as int? ?? 0;
        final unreadCount = folder['unreadCount'] as int? ?? 0;

        totalMessages += messageCount;
        totalUnread += unreadCount;

        // Categorize folders based on their names (comprehensive GMX categorization)
        if (folderName == 'inbox') {
          (structureMap['inbox'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isSentFolder(folderName)) {
          (structureMap['sent'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isSpamFolder(folderName)) {
          (structureMap['spam'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isTrashFolder(folderName)) {
          (structureMap['trash'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isDraftsFolder(folderName)) {
          (structureMap['drafts'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isArchiveFolder(folderName)) {
          (structureMap['archive'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isImportantFolder(folderName)) {
          (structureMap['important'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isTemplatesFolder(folderName)) {
          (structureMap['templates'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isNotesFolder(folderName)) {
          (structureMap['notes'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isCalendarFolder(folderName)) {
          (structureMap['calendar'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isContactsFolder(folderName)) {
          (structureMap['contacts'] as List<Map<String, dynamic>>).add(folder);
        } else if (_isOutboxFolder(folderName)) {
          (structureMap['outbox'] as List<Map<String, dynamic>>).add(folder);
        } else {
          (structureMap['custom'] as List<Map<String, dynamic>>).add(folder);
        }
      }

      structureMap['totalMessages'] = totalMessages;
      structureMap['totalUnread'] = totalUnread;

      _logger.i('GMX structure mapped: ${structureMap['totalFolders']} folders, $totalMessages total messages, $totalUnread unread');
      return structureMap;
    } catch (e, stack) {
      _logger.e('Error discovering and mapping GMX structure', error: e, stackTrace: stack);
      return {
        'inbox': <Map<String, dynamic>>[],
        'sent': <Map<String, dynamic>>[],
        'spam': <Map<String, dynamic>>[],
        'trash': <Map<String, dynamic>>[],
        'drafts': <Map<String, dynamic>>[],
        'archive': <Map<String, dynamic>>[],
        'custom': <Map<String, dynamic>>[],
        'totalFolders': 0,
        'totalMessages': 0,
        'totalUnread': 0,
        'error': e.toString(),
      };
    }
  }

  /// Get statistics for a specific mailbox
  Future<Map<String, dynamic>> _getMailboxStats(String mailboxName) async {
    try {
      final mailbox = await _imapClient.getMailboxStatus(mailboxName);

      if (mailbox == null) {
        return {
          'count': 0,
          'unread': 0,
          'size': 0,
        };
      }

      return {
        'count': mailbox.messagesExists,
        'unread': mailbox.messagesUnseen,
        'size': 0, // GMX doesn't provide size info easily via IMAP
      };
    } catch (e) {
      _logger.w('Error getting stats for mailbox $mailboxName: $e');
      return {
        'count': 0,
        'unread': 0,
        'size': 0,
      };
    }
  }

  /// Get display name for folder
  String _getFolderDisplayName(String folderName) {
    switch (folderName.toLowerCase()) {
      case 'inbox':
        return 'Courrier reçu';
      case 'sent':
      case 'gesendet':
      case 'envoyés':
        return 'Envoyés';
      case 'spam':
      case 'junk':
      case 'spamverdacht':
        return 'Spam';
      case 'trash':
      case 'deleted':
      case 'papierkorb':
      case 'corbeille':
        return 'Corbeille';
      case 'drafts':
      case 'entwürfe':
      case 'brouillons':
        return 'Brouillons';
      default:
        return folderName;
    }
  }

  // =======================================================
  // = Function: getEmails
  // = Description: Retrieves emails with optional query parameters and metadata
  // =======================================================
  Future<Map<String, dynamic>> getEmails({
    String? q,
    int? maxResults,
    String? pageToken,
    bool includeMetadata = true,
  }) async {
    try {
      await _ensureInitialized();
      _logger.d('Fetching GMX emails with query: $q');

      List<MimeMessage> messages;

      if (q != null && q.isNotEmpty) {
        messages = await _imapClient.searchMessages(
          mailboxName: 'INBOX',
          query: q,
          limit: maxResults ?? 50,
        );
      } else {
        messages = await _imapClient.fetchMessages(
          mailboxName: 'INBOX',
          limit: maxResults ?? 50,
          fetchStructure: includeMetadata,
        );
      }

      final emailList = <Map<String, dynamic>>[];
      for (final message in messages) {
        emailList.add(_convertMessageToMap(message));
      }

      return {
        'messages': emailList,
        'nextPageToken': null, // GMX doesn't support pagination like Gmail
        'resultSizeEstimate': emailList.length,
      };
    } catch (e, stack) {
      _logger.e('Error fetching GMX emails', error: e, stackTrace: stack);
      return {
        'messages': <Map<String, dynamic>>[],
        'nextPageToken': null,
        'resultSizeEstimate': 0,
      };
    }
  }

  // =======================================================
  // = Function: _convertMessageToMap
  // = Description: Converts MimeMessage to Map format compatible with Gmail API
  // =======================================================
  Map<String, dynamic> _convertMessageToMap(MimeMessage message) {
    try {
      return {
        'id': message.uid?.toString() ?? message.sequenceId?.toString() ?? 'unknown',
        'threadId': message.uid?.toString() ?? message.sequenceId?.toString() ?? 'unknown',
        'labelIds': _extractLabels(message),
        'snippet': _extractSnippet(message),
        'payload': _extractPayload(message),
        'sizeEstimate': message.size ?? 0,
        'historyId': message.uid?.toString(),
        'internalDate': message.decodeDate()?.millisecondsSinceEpoch.toString(),
      };
    } catch (e) {
      _logger.w('Error converting message to map: $e');
      return {
        'id': 'error',
        'threadId': 'error',
        'labelIds': <String>[],
        'snippet': 'Error loading message',
        'payload': {},
        'sizeEstimate': 0,
      };
    }
  }

  List<String> _extractLabels(MimeMessage message) {
    final labels = <String>['INBOX'];
    
    if (!message.isSeen) {
      labels.add('UNREAD');
    }
    
    if (message.isFlagged) {
      labels.add('STARRED');
    }
    
    return labels;
  }

  String _extractSnippet(MimeMessage message) {
    try {
      final text = message.decodeTextPlainPart() ??
                   message.decodeTextHtmlPart() ??
                   'No preview available';
      return text.length > 150 ? '${text.substring(0, 150)}...' : text;
    } catch (e) {
      return 'No preview available';
    }
  }

  Map<String, dynamic> _extractPayload(MimeMessage message) {
    try {
      final headers = <String, String>{};
      final messageHeaders = message.headers;
      if (messageHeaders != null && messageHeaders.isNotEmpty) {
        for (final header in messageHeaders) {
          headers[header.name] = header.value.toString();
        }
      }

      return {
        'headers': headers,
        'body': {
          'size': message.size ?? 0,
        },
        'filename': message.decodeSubject() ?? 'No subject',
        'mimeType': message.mediaType.toString(),
      };
    } catch (e) {
      return {
        'headers': <String, String>{},
        'body': {'size': 0},
        'filename': 'No subject',
        'mimeType': 'text/plain',
      };
    }
  }

  bool _hasAttachments(MimeMessage message) {
    try {
      final attachmentInfo = message.findContentInfo(disposition: ContentDisposition.attachment);
      return attachmentInfo.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  int _countAttachments(MimeMessage message) {
    try {
      // Count all parts with attachment disposition
      int count = 0;
      for (final part in message.allPartsFlat) {
        if (part.getHeaderContentDisposition()?.disposition == ContentDisposition.attachment) {
          count++;
        }
      }
      return count;
    } catch (e) {
      return 0;
    }
  }

  // =======================================================
  // = Function: getAttachmentAnalysis
  // = Description: Gets attachment analysis (count, emails with attachments, etc.)
  // =======================================================
  Future<Map<String, dynamic>> getAttachmentAnalysis({int maxSample = 50}) async {
    try {
      await _ensureInitialized();
      _logger.d('Analyzing GMX attachments');

      final messages = await _imapClient.fetchMessages(
        mailboxName: 'INBOX',
        limit: maxSample,
        fetchStructure: true,
      );

      int attachmentCount = 0;
      int emailsWithAttachments = 0;

      for (final message in messages) {
        final hasAttachments = _hasAttachments(message);
        if (hasAttachments) {
          emailsWithAttachments++;
          attachmentCount += _countAttachments(message);
        }
      }

      final result = {
        'hasAttachments': attachmentCount > 0,
        'totalAttachments': attachmentCount,
        'emailsWithAttachments': emailsWithAttachments,
        'sampleSize': messages.length,
      };

      _logger.i('GMX attachment analysis: $result');
      return result;
    } catch (e, stack) {
      _logger.e('Error analyzing GMX attachments', error: e, stackTrace: stack);
      return {
        'hasAttachments': false,
        'totalAttachments': 0,
        'emailsWithAttachments': 0,
        'sampleSize': 0,
      };
    }
  }

  // =======================================================
  // = Function: getTotalEmailCount
  // = Description: Gets total email count across all folders using comprehensive discovery
  // =======================================================
  Future<int> getTotalEmailCount() async {
    try {
      await _ensureInitialized();
      _logger.d('Getting total GMX email count using comprehensive discovery');

      // Use comprehensive discovery for accurate counts
      final folderStructure = await discoverAndMapGmxStructure();
      final totalCount = folderStructure['totalMessages'] as int? ?? 0;

      _logger.i('Total GMX emails (comprehensive discovery): $totalCount');
      return totalCount;
    } catch (e, stack) {
      _logger.e('Error getting total GMX email count with discovery', error: e, stackTrace: stack);

      // Fallback to basic method
      _logger.w('Falling back to basic email count method');
      return await _getTotalEmailCountBasic();
    }
  }

  // =======================================================
  // = Function: _getTotalEmailCountBasic
  // = Description: Fallback method for getting total email count
  // =======================================================
  Future<int> _getTotalEmailCountBasic() async {
    try {
      final mailboxes = await _imapClient.getMailboxes();
      int totalCount = 0;
      for (final mailbox in mailboxes) {
        try {
          final stats = await _getMailboxStats(mailbox.name);
          totalCount += ((stats['count'] ?? 0) as num).toInt();
        } catch (e) {
          _logger.w('Error getting count for folder ${mailbox.name}: $e');
        }
      }
      _logger.i('Total GMX emails (basic method): $totalCount');
      return totalCount;
    } catch (e, stack) {
      _logger.e('Error in basic email count method', error: e, stackTrace: stack);
      return 0;
    }
  }

  // =======================================================
  // = Function: getInboxStats
  // = Description: Gets inbox statistics (for compatibility with Gmail API)
  // =======================================================
  Future<Map<String, dynamic>> getInboxStats() async {
    return await getCategoryStats('inbox');
  }

  // =======================================================
  // = Function: getSpamStats
  // = Description: Gets spam statistics (for compatibility with Gmail API)
  // =======================================================
  Future<Map<String, dynamic>> getSpamStats() async {
    return await getCategoryStats('spam');
  }

  // =======================================================
  // = Function: dispose
  // = Description: Cleans up resources
  // =======================================================
  Future<void> dispose() async {
    try {
      if (_initialized) {
        await _imapClient.disconnect();
        _initialized = false;
        _logger.i('GMX API service disposed');
      }
    } catch (e) {
      _logger.e('Error disposing GMX API service', error: e);
    }
  }

  // Helper: Map standard category to GMX folder name
  String _resolveGmxFolderName(String category) {
    switch (category.toLowerCase()) {
      case 'inbox':
      case 'primary':
      case 'courrier_recu':
        return 'INBOX';
      case 'sent':
      case 'envoyes':
      case 'gesendet':
        return 'Sent';
      case 'spam':
      case 'junk':
      case 'spamverdacht':
        return 'Spam';
      case 'trash':
      case 'deleted':
      case 'papierkorb':
      case 'corbeille':
        return 'Trash';
      case 'drafts':
      case 'entwürfe':
      case 'brouillons':
        return 'Drafts';
      default:
        return category;
    }
  }

  // =======================================================
  // = Helper Functions: Folder Type Detection
  // = Description: Helper methods to identify folder types based on names
  // =======================================================

  bool _isSentFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'sent' ||
           lowerName == 'gesendet' ||
           lowerName == 'envoyés' ||
           lowerName == 'sent items' ||
           lowerName == 'gesendete objekte';
  }

  bool _isSpamFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'spam' ||
           lowerName == 'junk' ||
           lowerName == 'spamverdacht' ||
           lowerName == 'junk-e-mail' ||
           lowerName == 'courrier indésirable';
  }

  bool _isTrashFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'trash' ||
           lowerName == 'deleted' ||
           lowerName == 'papierkorb' ||
           lowerName == 'corbeille' ||
           lowerName == 'deleted items' ||
           lowerName == 'gelöschte objekte';
  }

  bool _isDraftsFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'drafts' ||
           lowerName == 'entwürfe' ||
           lowerName == 'brouillons' ||
           lowerName == 'draft';
  }

  bool _isArchiveFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'archive' ||
           lowerName == 'archiv' ||
           lowerName == 'archives' ||
           lowerName == 'archived' ||
           lowerName == 'archiviert' ||
           lowerName == 'archivé' ||
           lowerName.contains('archiv');
  }

  bool _isImportantFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'important' ||
           lowerName == 'wichtig' ||
           lowerName == 'priority' ||
           lowerName == 'priorité' ||
           lowerName == 'high priority' ||
           lowerName == 'starred' ||
           lowerName == 'favoris' ||
           lowerName == 'favorites' ||
           lowerName == 'flagged' ||
           lowerName.contains('important') ||
           lowerName.contains('priority');
  }

  bool _isTemplatesFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'templates' ||
           lowerName == 'vorlagen' ||
           lowerName == 'modèles' ||
           lowerName == 'template' ||
           lowerName == 'vorlage' ||
           lowerName == 'modèle' ||
           lowerName.contains('template') ||
           lowerName.contains('vorlage');
  }

  bool _isNotesFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'notes' ||
           lowerName == 'notizen' ||
           lowerName == 'note' ||
           lowerName == 'notiz' ||
           lowerName.contains('note');
  }

  bool _isCalendarFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'calendar' ||
           lowerName == 'kalender' ||
           lowerName == 'calendrier' ||
           lowerName == 'tasks' ||
           lowerName == 'aufgaben' ||
           lowerName == 'tâches' ||
           lowerName == 'termine' ||
           lowerName == 'rendez-vous' ||
           lowerName.contains('calendar') ||
           lowerName.contains('task');
  }

  bool _isContactsFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'contacts' ||
           lowerName == 'kontakte' ||
           lowerName == 'contact' ||
           lowerName == 'kontakt' ||
           lowerName == 'adresses' ||
           lowerName == 'adressen' ||
           lowerName.contains('contact') ||
           lowerName.contains('kontakt');
  }

  bool _isOutboxFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'outbox' ||
           lowerName == 'ausgang' ||
           lowerName == 'sortant' ||
           lowerName == 'boîte d\'envoi' ||
           lowerName == 'zu senden' ||
           lowerName == 'à envoyer' ||
           lowerName.contains('outbox') ||
           lowerName.contains('ausgang');
  }

  // =======================================================
  // = Function: getDetailedStructureReport
  // = Description: Gets a detailed report of the GMX structure discovery
  // =======================================================
  Future<Map<String, dynamic>> getDetailedStructureReport() async {
    try {
      await _ensureInitialized();
      _logger.d('Generating detailed GMX structure report');

      final folderStructure = await discoverAndMapGmxStructure();
      final allFolders = await getAllFolders();

      // Generate comprehensive report
      final report = {
        'timestamp': DateTime.now().toIso8601String(),
        'summary': {
          'totalFolders': folderStructure['totalFolders'],
          'totalMessages': folderStructure['totalMessages'],
          'totalUnread': folderStructure['totalUnread'],
          'selectableFolders': allFolders.where((f) => f['isSelectable'] == true).length,
        },
        'categoryBreakdown': {
          'inbox': {
            'folders': folderStructure['inbox'],
            'count': (folderStructure['inbox'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['inbox'] as List<Map<String, dynamic>>),
          },
          'sent': {
            'folders': folderStructure['sent'],
            'count': (folderStructure['sent'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['sent'] as List<Map<String, dynamic>>),
          },
          'spam': {
            'folders': folderStructure['spam'],
            'count': (folderStructure['spam'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['spam'] as List<Map<String, dynamic>>),
          },
          'trash': {
            'folders': folderStructure['trash'],
            'count': (folderStructure['trash'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['trash'] as List<Map<String, dynamic>>),
          },
          'drafts': {
            'folders': folderStructure['drafts'],
            'count': (folderStructure['drafts'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['drafts'] as List<Map<String, dynamic>>),
          },
          'archive': {
            'folders': folderStructure['archive'],
            'count': (folderStructure['archive'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['archive'] as List<Map<String, dynamic>>),
          },
          'important': {
            'folders': folderStructure['important'],
            'count': (folderStructure['important'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['important'] as List<Map<String, dynamic>>),
          },
          'templates': {
            'folders': folderStructure['templates'],
            'count': (folderStructure['templates'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['templates'] as List<Map<String, dynamic>>),
          },
          'notes': {
            'folders': folderStructure['notes'],
            'count': (folderStructure['notes'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['notes'] as List<Map<String, dynamic>>),
          },
          'calendar': {
            'folders': folderStructure['calendar'],
            'count': (folderStructure['calendar'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['calendar'] as List<Map<String, dynamic>>),
          },
          'contacts': {
            'folders': folderStructure['contacts'],
            'count': (folderStructure['contacts'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['contacts'] as List<Map<String, dynamic>>),
          },
          'outbox': {
            'folders': folderStructure['outbox'],
            'count': (folderStructure['outbox'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['outbox'] as List<Map<String, dynamic>>),
          },
          'custom': {
            'folders': folderStructure['custom'],
            'count': (folderStructure['custom'] as List).length,
            'totalMessages': _sumMessagesInCategory(folderStructure['custom'] as List<Map<String, dynamic>>),
          },
        },
        'allFolders': allFolders,
        'discoveryMethod': 'comprehensive',
        'status': 'success',
      };

      _logger.i('Detailed GMX structure report generated successfully');
      return report;
    } catch (e, stack) {
      _logger.e('Error generating detailed structure report', error: e, stackTrace: stack);
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'status': 'error',
        'error': e.toString(),
        'summary': {
          'totalFolders': 0,
          'totalMessages': 0,
          'totalUnread': 0,
          'selectableFolders': 0,
        },
      };
    }
  }

  // =======================================================
  // = Function: _sumMessagesInCategory
  // = Description: Helper to sum messages in a category
  // =======================================================
  int _sumMessagesInCategory(List<Map<String, dynamic>> folders) {
    return folders.fold(0, (sum, folder) => sum + (folder['messageCount'] as int? ?? 0));
  }
}

// =======================================================
// = Class: GmxOnlineMode
// = Description: Wrapper class for GMX online mode operations
// = This class provides a unified interface for GMX operations
// =======================================================
class GmxOnlineMode {
  GmxApiService? _apiService;
  final Logger _logger = Logger();

  /// Initialize the GMX online mode with a user
  Future<void> initialize(dynamic gmxUser) async {
    _apiService = GmxApiService(gmxUser);
    _logger.d('GmxOnlineMode initialized');
  }

  /// Discover and map GMX structure
  Future<Map<String, dynamic>> discoverAndMapGmxStructure() async {
    if (_apiService == null) {
      throw Exception('GmxOnlineMode not initialized');
    }
    return await _apiService!.discoverAndMapGmxStructure();
  }

  /// Get all folders
  Future<List<Map<String, dynamic>>> getAllFolders() async {
    if (_apiService == null) {
      throw Exception('GmxOnlineMode not initialized');
    }
    return await _apiService!.getAllFolders();
  }

  /// Get attachment analysis
  Future<Map<String, dynamic>> getAttachmentAnalysis({int maxSample = 50}) async {
    if (_apiService == null) {
      throw Exception('GmxOnlineMode not initialized');
    }
    return await _apiService!.getAttachmentAnalysis(maxSample: maxSample);
  }

  /// Get total email count
  Future<int> getTotalEmailCount() async {
    if (_apiService == null) {
      throw Exception('GmxOnlineMode not initialized');
    }
    return await _apiService!.getTotalEmailCount();
  }

  /// Get detailed structure report
  Future<Map<String, dynamic>> getDetailedStructureReport() async {
    if (_apiService == null) {
      throw Exception('GmxOnlineMode not initialized');
    }
    return await _apiService!.getDetailedStructureReport();
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_apiService != null) {
      await _apiService!.dispose();
      _apiService = null;
    }
  }
}
