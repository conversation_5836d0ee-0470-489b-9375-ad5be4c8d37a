/*
=======================================================
= File: gmx_imap_client.dart
= Project: LavaMail
= Description:
=   - GMX IMAP client for email operations
=   - Handles email fetching, folder management, and message operations
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:enough_mail/enough_mail.dart';
import '../auth/gmx_auth_service.dart';

// =======================================================
// = Class: GmxImapClient
// = Description: IMAP client wrapper for GMX email operations
// =======================================================
class GmxImapClient {
  final Logger _logger = Logger();
  ImapClient? _client;
  bool _isConnected = false;

  // =======================================================
  // = Function: connect
  // = Description: Establishes connection to GMX IMAP server
  // =======================================================
  Future<void> connect() async {
    try {
      if (_isConnected && _client != null) {
        return; // Already connected
      }

      _logger.d('Connecting to GMX IMAP server');
      _client = await GmxAuthService.getImapClient();
      _isConnected = true;
      _logger.i('Connected to GMX IMAP server successfully');
    } catch (e, stack) {
      _logger.e('Failed to connect to GMX IMAP server', error: e, stackTrace: stack);
      _isConnected = false;
      rethrow;
    }
  }

  // =======================================================
  // = Function: disconnect
  // = Description: Disconnects from IMAP server
  // =======================================================
  Future<void> disconnect() async {
    try {
      if (_client != null && _isConnected) {
        await _client!.disconnect();
        _logger.i('Disconnected from GMX IMAP server');
      }
    } catch (e) {
      _logger.w('Error disconnecting from IMAP server: $e');
    } finally {
      _client = null;
      _isConnected = false;
    }
  }

  // =======================================================
  // = Function: _ensureConnected
  // = Description: Ensures IMAP connection is established
  // =======================================================
  Future<void> _ensureConnected() async {
    if (!_isConnected || _client == null) {
      await connect();
    }
  }

  // =======================================================
  // = Function: fetchMessages
  // = Description: Fetches messages from specified mailbox
  // =======================================================
  Future<List<MimeMessage>> fetchMessages({
    String mailboxName = 'INBOX',
    int? limit,
    bool fetchStructure = false,
  }) async {
    try {
      await _ensureConnected();
      _logger.d('Fetching messages from $mailboxName (limit: $limit)');

      final mailbox = await _client!.selectInbox();

      final messageCount = mailbox.messagesExists;
      if (messageCount == 0) {
        _logger.d('No messages found in $mailboxName');
        return [];
      }

      // Calculate range for fetching
      final fetchLimit = limit ?? messageCount;
      final startIndex = messageCount > fetchLimit ? messageCount - fetchLimit + 1 : 1;
      final endIndex = messageCount;

      _logger.d('Fetching messages $startIndex to $endIndex from $mailboxName');

      final fetchResult = await _client!.fetchMessages(
        MessageSequence.fromRange(startIndex, endIndex),
        fetchStructure ? 'BODY.PEEK[]' : 'ENVELOPE',
      );

      final messages = fetchResult.messages;
      _logger.i('Fetched ${messages.length} messages from $mailboxName');
      
      return messages;
    } catch (e, stack) {
      _logger.e('Failed to fetch messages from $mailboxName', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: searchMessages
  // = Description: Searches for messages matching criteria
  // =======================================================
  Future<List<MimeMessage>> searchMessages({
    String mailboxName = 'INBOX',
    String? query,
    int? limit,
  }) async {
    try {
      await _ensureConnected();
      _logger.d('Searching messages in $mailboxName with query: $query');

      await _client!.selectInbox();

      SearchImapResult searchResult;
      if (query != null && query.isNotEmpty) {
        // Simple text search using SEARCH command
        searchResult = await _client!.searchMessages(searchCriteria: 'TEXT "$query"');
      } else {
        // Search all messages
        searchResult = await _client!.searchMessages(searchCriteria: 'ALL');
      }

      if (searchResult.matchingSequence?.isEmpty ?? true) {
        _logger.d('No messages found matching search criteria');
        return [];
      }

      // Fetch the matching messages
      final sequence = searchResult.matchingSequence!;
      final fetchLimit = limit != null ? 
        MessageSequence.fromIds(sequence.toList().take(limit).toList()) : 
        sequence;

      final fetchResult = await _client!.fetchMessages(
        fetchLimit,
        'BODY.PEEK[]',
      );

      List<MimeMessage> messages = fetchResult.messages;

      // Simple local filtering if query is provided
      if (query != null && query.isNotEmpty) {
        final queryLower = query.toLowerCase();
        messages = messages.where((message) {
          final subject = message.decodeSubject()?.toLowerCase() ?? '';
          final from = message.from?.toString().toLowerCase() ?? '';
          return subject.contains(queryLower) || from.contains(queryLower);
        }).toList();
      }

      // Apply limit if specified
      if (limit != null && messages.length > limit) {
        messages = messages.take(limit).toList();
      }

      _logger.i('Found ${messages.length} messages matching search criteria');
      return messages;
    } catch (e) {
      _logger.e('Failed to search messages in $mailboxName', error: e);
      return [];
    }
  }

  // =======================================================
  // = Function: getMailboxes
  // = Description: Gets list of available mailboxes
  // =======================================================
  Future<List<Mailbox>> getMailboxes() async {
    try {
      await _ensureConnected();
      _logger.d('Fetching mailbox list');

      final listResult = await _client!.listMailboxes();
      final mailboxes = listResult;
      
      _logger.i('Found ${mailboxes.length} mailboxes');
      return mailboxes;
    } catch (e, stack) {
      _logger.e('Failed to fetch mailboxes', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: getMailboxStatus
  // = Description: Gets status information for a mailbox
  // =======================================================
  Future<Mailbox?> getMailboxStatus(String mailboxName) async {
    try {
      await _ensureConnected();
      _logger.d('Getting status for mailbox: $mailboxName');

      // Try to select the mailbox to get accurate message counts
      final mailbox = await selectMailbox(mailboxName);

      if (mailbox != null) {
        _logger.d('Mailbox $mailboxName: ${mailbox.messagesExists} messages, ${mailbox.messagesUnseen} unread');
        return mailbox;
      }

      _logger.w('Mailbox $mailboxName not found or could not be selected');
      return null;
    } catch (e, stack) {
      _logger.e('Failed to get mailbox status for $mailboxName', error: e, stackTrace: stack);
      return null;
    }
  }

  // =======================================================
  // = Function: selectMailbox
  // = Description: Selects a specific mailbox for operations
  // =======================================================
  Future<Mailbox?> selectMailbox(String mailboxName) async {
    try {
      await _ensureConnected();
      _logger.d('Selecting mailbox: $mailboxName');

      // Handle INBOX specially for compatibility
      if (mailboxName.toUpperCase() == 'INBOX') {
        final mailbox = await _client!.selectInbox();
        _logger.d('Selected mailbox $mailboxName successfully');
        return mailbox;
      }

      // For other mailboxes, find the mailbox object first, then select it
      final mailboxes = await _client!.listMailboxes();
      final targetMailbox = mailboxes.where(
        (mb) => mb.name.toLowerCase() == mailboxName.toLowerCase(),
      ).firstOrNull;

      if (targetMailbox != null) {
        try {
          final selectedMailbox = await _client!.selectMailbox(targetMailbox);
          _logger.d('Selected mailbox ${targetMailbox.name} successfully: ${selectedMailbox.messagesExists} messages');
          return selectedMailbox;
        } catch (e) {
          _logger.w('Failed to select mailbox ${targetMailbox.name}: $e');
          return null;
        }
      }

      _logger.w('Mailbox $mailboxName not found');
      return null;
    } catch (e, stack) {
      _logger.e('Failed to select mailbox $mailboxName', error: e, stackTrace: stack);
      return null;
    }
  }

  // =======================================================
  // = Function: markAsRead
  // = Description: Marks a message as read
  // =======================================================
  Future<bool> markAsRead(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot mark message as read: no UID');
        return false;
      }

      await _client!.markSeen(MessageSequence.fromId(message.uid!), silent: true);
      _logger.d('Marked message ${message.uid} as read');
      return true;
    } catch (e) {
      _logger.e('Failed to mark message as read', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: markAsUnread
  // = Description: Marks a message as unread
  // =======================================================
  Future<bool> markAsUnread(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot mark message as unread: no UID');
        return false;
      }

      await _client!.markUnseen(MessageSequence.fromId(message.uid!), silent: true);
      _logger.d('Marked message ${message.uid} as unread');
      return true;
    } catch (e) {
      _logger.e('Failed to mark message as unread', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: deleteMessage
  // = Description: Deletes a message
  // =======================================================
  Future<bool> deleteMessage(MimeMessage message) async {
    try {
      await _ensureConnected();

      if (message.uid == null) {
        _logger.w('Cannot delete message: no UID');
        return false;
      }

      await _client!.markDeleted(MessageSequence.fromId(message.uid!));
      await _client!.expunge();
      _logger.d('Deleted message ${message.uid}');
      return true;
    } catch (e) {
      _logger.e('Failed to delete message', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: discoverFolderStructure
  // = Description: Discovers the complete GMX folder structure with detailed analysis
  // = Inspired by the Python gmx.py script for comprehensive folder discovery
  // =======================================================
  Future<List<Map<String, dynamic>>> discoverFolderStructure() async {
    try {
      await _ensureConnected();
      _logger.d('Starting comprehensive GMX folder structure discovery');

      // Step 1: Get all mailboxes using multiple methods for robustness
      final listResult = await _getMailboxesRobust();
      final discoveredFolders = <Map<String, dynamic>>[];

      _logger.i('=== DETAILED GMX FOLDER ANALYSIS ===');
      _logger.i('Total number of folders: ${listResult.length}');

      // Step 2: Analyze each folder in detail
      int totalMessages = 0;
      int totalUnread = 0;
      int selectableFolders = 0;

      for (final mailbox in listResult) {
        try {
          final folderInfo = await _parseDetailedFolderInfo(mailbox);
          discoveredFolders.add(folderInfo);

          // Accumulate totals
          final messageCount = folderInfo['messageCount'] as int? ?? 0;
          final unreadCount = folderInfo['unreadCount'] as int? ?? 0;
          totalMessages += messageCount;
          totalUnread += unreadCount;

          if (folderInfo['isSelectable'] == true) {
            selectableFolders++;
          }

          _logger.d('Name: \'${folderInfo['name']}\'');
          _logger.d('Display Name: \'${folderInfo['displayName']}\'');
          _logger.d('Path: \'${folderInfo['path']}\'');
          _logger.d('Flags: ${folderInfo['flags']}');
          _logger.d('Message Count: ${folderInfo['messageCount']}');
          _logger.d('Unread Count: ${folderInfo['unreadCount']}');
          _logger.d('Is Selectable: ${folderInfo['isSelectable']}');
          _logger.d('-' * 50);
        } catch (e) {
          _logger.w('Error analyzing folder ${mailbox.name}: $e');
          // Add basic info even if detailed analysis fails
          discoveredFolders.add({
            'name': mailbox.name,
            'displayName': _getFolderDisplayName(mailbox.name),
            'path': mailbox.path,
            'flags': <String>[],
            'messageCount': 0,
            'unreadCount': 0,
            'isSelectable': false,
            'hasChildren': mailbox.hasChildren,
            'error': e.toString(),
          });
        }
      }

      // Step 3: Log comprehensive summary
      _logger.i('GMX folder structure discovery completed:');
      _logger.i('- Total folders discovered: ${discoveredFolders.length}');
      _logger.i('- Selectable folders: $selectableFolders');
      _logger.i('- Total messages across all folders: $totalMessages');
      _logger.i('- Total unread messages: $totalUnread');

      // Step 4: Validate discovery results
      _validateDiscoveryResults(discoveredFolders, totalMessages);

      return discoveredFolders;
    } catch (e, stack) {
      _logger.e('Failed to discover GMX folder structure', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: _getMailboxesRobust
  // = Description: Gets mailboxes using multiple methods for maximum compatibility
  // =======================================================
  Future<List<Mailbox>> _getMailboxesRobust() async {
    try {
      // Method 1: Standard listMailboxes
      _logger.d('Attempting standard mailbox listing...');
      final standardResult = await _client!.listMailboxes();
      if (standardResult.isNotEmpty) {
        _logger.i('Standard mailbox listing successful: ${standardResult.length} folders');
        return standardResult;
      }
    } catch (e) {
      _logger.w('Standard mailbox listing failed: $e');
    }

    try {
      // Method 2: List with specific pattern (GMX specific)
      _logger.d('Attempting pattern-based mailbox listing...');
      final patternResult = await _client!.listMailboxes(path: '*');
      if (patternResult.isNotEmpty) {
        _logger.i('Pattern-based mailbox listing successful: ${patternResult.length} folders');
        return patternResult;
      }
    } catch (e) {
      _logger.w('Pattern-based mailbox listing failed: $e');
    }

    try {
      // Method 3: List with empty path (fallback)
      _logger.d('Attempting fallback mailbox listing...');
      final fallbackResult = await _client!.listMailboxes(path: '');
      _logger.i('Fallback mailbox listing: ${fallbackResult.length} folders');
      return fallbackResult;
    } catch (e) {
      _logger.e('All mailbox listing methods failed: $e');
      return [];
    }
  }

  // =======================================================
  // = Function: _parseDetailedFolderInfo
  // = Description: Parses detailed information for a single folder
  // = Similar to parse_folder_info in the Python script
  // =======================================================
  Future<Map<String, dynamic>> _parseDetailedFolderInfo(Mailbox mailbox) async {
    try {
      // Get basic folder information
      final folderInfo = {
        'name': mailbox.name,
        'displayName': _getFolderDisplayName(mailbox.name),
        'path': mailbox.path,
        'flags': mailbox.flags.map((flag) => flag.name).toList(),
        'hasChildren': mailbox.hasChildren,
        'isSelectable': true,
        'messageCount': 0,
        'unreadCount': 0,
        'separator': mailbox.pathSeparator,
        'folderType': _determineFolderType(mailbox.name),
        'rawInfo': '${mailbox.name} (${mailbox.path})',
      };

      // Try multiple methods to get message counts
      bool statsObtained = false;

      // Method 1: Try to select the mailbox for accurate counts
      try {
        final selectedMailbox = await selectMailbox(mailbox.name);
        if (selectedMailbox != null) {
          folderInfo['messageCount'] = selectedMailbox.messagesExists;
          folderInfo['unreadCount'] = selectedMailbox.messagesUnseen;
          folderInfo['isSelectable'] = true;
          statsObtained = true;
          _logger.d('Selected ${mailbox.name}: ${selectedMailbox.messagesExists} messages, ${selectedMailbox.messagesUnseen} unread');
        }
      } catch (e) {
        _logger.w('Cannot select folder ${mailbox.name}: $e');
      }

      // Method 2: Try to get basic info from mailbox properties if selection failed
      if (!statsObtained) {
        try {
          // Check if the mailbox has basic message count information
          // Some IMAP servers provide this in the LIST response
          if (mailbox.messagesExists > 0) {
            folderInfo['messageCount'] = mailbox.messagesExists;
            folderInfo['unreadCount'] = mailbox.messagesUnseen;
            folderInfo['isSelectable'] = true;
            statsObtained = true;
            _logger.d('Basic info ${mailbox.name}: ${mailbox.messagesExists} messages, ${mailbox.messagesUnseen} unread');
          }
        } catch (e) {
          _logger.w('Cannot get basic info for ${mailbox.name}: $e');
        }
      }

      // Method 3: Check if folder is selectable based on flags
      if (!statsObtained) {
        final flags = mailbox.flags.map((f) => f.name.toLowerCase()).toList();
        final isNoSelect = flags.contains('\\noselect') || flags.contains('noselect');
        folderInfo['isSelectable'] = !isNoSelect;

        if (isNoSelect) {
          _logger.d('Folder ${mailbox.name} marked as \\NoSelect');
        } else {
          folderInfo['selectError'] = 'Could not retrieve message counts';
        }
      }

      return folderInfo;
    } catch (e) {
      _logger.e('Error parsing folder info for ${mailbox.name}', error: e);
      rethrow;
    }
  }

  // =======================================================
  // = Function: _determineFolderType
  // = Description: Determines the type of folder based on name and flags
  // =======================================================
  String _determineFolderType(String folderName) {
    final lowerName = folderName.toLowerCase();

    if (lowerName == 'inbox') return 'inbox';
    if (_isSentFolder(lowerName)) return 'sent';
    if (_isSpamFolder(lowerName)) return 'spam';
    if (_isTrashFolder(lowerName)) return 'trash';
    if (_isDraftsFolder(lowerName)) return 'drafts';
    if (_isArchiveFolder(lowerName)) return 'archive';

    return 'custom';
  }

  // Helper methods for folder type detection
  bool _isSentFolder(String folderName) {
    return folderName == 'sent' ||
           folderName == 'gesendet' ||
           folderName == 'envoyés' ||
           folderName == 'sent items' ||
           folderName == 'gesendete objekte';
  }

  bool _isSpamFolder(String folderName) {
    return folderName == 'spam' ||
           folderName == 'junk' ||
           folderName == 'spamverdacht' ||
           folderName == 'junk-e-mail' ||
           folderName == 'courrier indésirable';
  }

  bool _isTrashFolder(String folderName) {
    return folderName == 'trash' ||
           folderName == 'deleted' ||
           folderName == 'papierkorb' ||
           folderName == 'corbeille' ||
           folderName == 'deleted items' ||
           folderName == 'gelöschte objekte';
  }

  bool _isDraftsFolder(String folderName) {
    return folderName == 'drafts' ||
           folderName == 'entwürfe' ||
           folderName == 'brouillons' ||
           folderName == 'draft';
  }

  bool _isArchiveFolder(String folderName) {
    return folderName == 'archive' ||
           folderName == 'archiv' ||
           folderName == 'archives';
  }

  // =======================================================
  // = Function: _validateDiscoveryResults
  // = Description: Validates the discovery results and logs warnings if needed
  // =======================================================
  void _validateDiscoveryResults(List<Map<String, dynamic>> folders, int totalMessages) {
    // Check if we found essential folders
    final folderNames = folders.map((f) => (f['name'] as String).toLowerCase()).toList();

    bool hasInbox = folderNames.contains('inbox');
    bool hasSent = folders.any((f) => _isSentFolder((f['name'] as String).toLowerCase()));
    bool hasSpam = folders.any((f) => _isSpamFolder((f['name'] as String).toLowerCase()));

    _logger.i('=== DISCOVERY VALIDATION ===');
    _logger.i('Essential folders found:');
    _logger.i('- INBOX: ${hasInbox ? "✓" : "✗"}');
    _logger.i('- Sent folder: ${hasSent ? "✓" : "✗"}');
    _logger.i('- Spam folder: ${hasSpam ? "✓" : "✗"}');

    if (!hasInbox) {
      _logger.w('WARNING: INBOX folder not found! This is unusual for GMX.');
    }

    if (folders.isEmpty) {
      _logger.e('ERROR: No folders discovered! Check IMAP connection and permissions.');
    } else if (totalMessages == 0) {
      _logger.w('WARNING: No messages found in any folder. Account might be empty or permissions limited.');
    }

    // Log folder type distribution
    final folderTypes = <String, int>{};
    for (final folder in folders) {
      final type = folder['folderType'] as String? ?? 'unknown';
      folderTypes[type] = (folderTypes[type] ?? 0) + 1;
    }

    _logger.i('Folder type distribution: $folderTypes');
  }

  // =======================================================
  // = Function: _getFolderDisplayName
  // = Description: Maps GMX folder names to user-friendly display names
  // = Handles German, French, English, and other language folder names
  // =======================================================
  String _getFolderDisplayName(String folderName) {
    final lowerName = folderName.toLowerCase();

    // Standard IMAP folders
    if (lowerName == 'inbox') return 'Courrier reçu';

    // Sent folders (various languages and variations)
    if (lowerName == 'sent' ||
        lowerName == 'gesendet' ||
        lowerName == 'envoyés' ||
        lowerName == 'sent items' ||
        lowerName == 'gesendete objekte' ||
        lowerName == 'gesendete elemente' ||
        lowerName == 'sent mail' ||
        lowerName == 'outbox' ||
        lowerName == 'sortant') {
      return 'Envoyés';
    }

    // Spam/Junk folders (comprehensive list)
    if (lowerName == 'spam' ||
        lowerName == 'junk' ||
        lowerName == 'spamverdacht' ||
        lowerName == 'junk-e-mail' ||
        lowerName == 'courrier indésirable' ||
        lowerName == 'bulk mail' ||
        lowerName == 'junk mail' ||
        lowerName == 'unwanted' ||
        lowerName == 'spamassassin' ||
        lowerName == 'quarantine' ||
        lowerName == 'quarantäne') {
      return 'Spam';
    }

    // Trash/Deleted folders (comprehensive list)
    if (lowerName == 'trash' ||
        lowerName == 'deleted' ||
        lowerName == 'papierkorb' ||
        lowerName == 'corbeille' ||
        lowerName == 'deleted items' ||
        lowerName == 'gelöschte objekte' ||
        lowerName == 'gelöschte elemente' ||
        lowerName == 'bin' ||
        lowerName == 'recycle bin' ||
        lowerName == 'wastebasket' ||
        lowerName == 'poubelle') {
      return 'Corbeille';
    }

    // Draft folders (comprehensive list)
    if (lowerName == 'drafts' ||
        lowerName == 'entwürfe' ||
        lowerName == 'brouillons' ||
        lowerName == 'draft' ||
        lowerName == 'entwurf' ||
        lowerName == 'unsent' ||
        lowerName == 'unsent messages') {
      return 'Brouillons';
    }

    // Archive folders (comprehensive list)
    if (lowerName == 'archive' ||
        lowerName == 'archiv' ||
        lowerName == 'archives' ||
        lowerName == 'archived' ||
        lowerName == 'archiviert' ||
        lowerName == 'archivé' ||
        lowerName == 'old mail' ||
        lowerName == 'alte nachrichten') {
      return 'Archives';
    }

    // Important/Priority folders
    if (lowerName == 'important' ||
        lowerName == 'wichtig' ||
        lowerName == 'priority' ||
        lowerName == 'priorité' ||
        lowerName == 'high priority' ||
        lowerName == 'starred' ||
        lowerName == 'favoris' ||
        lowerName == 'favorites') {
      return 'Important';
    }

    // Templates folders
    if (lowerName == 'templates' ||
        lowerName == 'vorlagen' ||
        lowerName == 'modèles' ||
        lowerName == 'template') {
      return 'Modèles';
    }

    // Notes folders
    if (lowerName == 'notes' ||
        lowerName == 'notizen' ||
        lowerName == 'note') {
      return 'Notes';
    }

    // Calendar/Tasks folders
    if (lowerName == 'calendar' ||
        lowerName == 'kalender' ||
        lowerName == 'calendrier' ||
        lowerName == 'tasks' ||
        lowerName == 'aufgaben' ||
        lowerName == 'tâches') {
      return 'Calendrier/Tâches';
    }

    // Return original name if no mapping found, but capitalize first letter
    return folderName.isNotEmpty
        ? '${folderName[0].toUpperCase()}${folderName.substring(1)}'
        : folderName;
  }
}
