/*
=======================================================
= File: gmx_imap_client.dart
= Project: LavaMail
= Description:
=   - GMX IMAP client for email operations
=   - Handles email fetching, folder management, and message operations
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:enough_mail/enough_mail.dart';
import '../auth/gmx_auth_service.dart';

// =======================================================
// = Class: GmxImapClient
// = Description: IMAP client wrapper for GMX email operations
// =======================================================
class GmxImapClient {
  final Logger _logger = Logger();
  ImapClient? _client;
  bool _isConnected = false;

  // =======================================================
  // = Function: connect
  // = Description: Establishes connection to GMX IMAP server
  // =======================================================
  Future<void> connect() async {
    try {
      if (_isConnected && _client != null) {
        return; // Already connected
      }

      _logger.d('Connecting to GMX IMAP server');
      _client = await GmxAuthService.getImapClient();
      _isConnected = true;
      _logger.i('Connected to GMX IMAP server successfully');
    } catch (e, stack) {
      _logger.e('Failed to connect to GMX IMAP server', error: e, stackTrace: stack);
      _isConnected = false;
      rethrow;
    }
  }

  // =======================================================
  // = Function: disconnect
  // = Description: Disconnects from IMAP server
  // =======================================================
  Future<void> disconnect() async {
    try {
      if (_client != null && _isConnected) {
        await _client!.disconnect();
        _logger.i('Disconnected from GMX IMAP server');
      }
    } catch (e) {
      _logger.w('Error disconnecting from IMAP server: $e');
    } finally {
      _client = null;
      _isConnected = false;
    }
  }

  // =======================================================
  // = Function: _ensureConnected
  // = Description: Ensures IMAP connection is established
  // =======================================================
  Future<void> _ensureConnected() async {
    if (!_isConnected || _client == null) {
      await connect();
    }
  }

  // =======================================================
  // = Function: fetchMessages
  // = Description: Fetches messages from specified mailbox
  // =======================================================
  Future<List<MimeMessage>> fetchMessages({
    String mailboxName = 'INBOX',
    int? limit,
    bool fetchStructure = false,
  }) async {
    try {
      await _ensureConnected();
      _logger.d('Fetching messages from $mailboxName (limit: $limit)');

      final mailbox = await _client!.selectInbox();

      final messageCount = mailbox.messagesExists;
      if (messageCount == 0) {
        _logger.d('No messages found in $mailboxName');
        return [];
      }

      // Calculate range for fetching
      final fetchLimit = limit ?? messageCount;
      final startIndex = messageCount > fetchLimit ? messageCount - fetchLimit + 1 : 1;
      final endIndex = messageCount;

      _logger.d('Fetching messages $startIndex to $endIndex from $mailboxName');

      final fetchResult = await _client!.fetchMessages(
        MessageSequence.fromRange(startIndex, endIndex),
        fetchStructure ? 'BODY.PEEK[]' : 'ENVELOPE',
      );

      final messages = fetchResult.messages;
      _logger.i('Fetched ${messages.length} messages from $mailboxName');
      
      return messages;
    } catch (e, stack) {
      _logger.e('Failed to fetch messages from $mailboxName', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: searchMessages
  // = Description: Searches for messages matching criteria
  // =======================================================
  Future<List<MimeMessage>> searchMessages({
    String mailboxName = 'INBOX',
    String? query,
    int? limit,
  }) async {
    try {
      await _ensureConnected();
      _logger.d('Searching messages in $mailboxName with query: $query');

      await _client!.selectInbox();

      SearchImapResult searchResult;
      if (query != null && query.isNotEmpty) {
        // Simple text search using SEARCH command
        searchResult = await _client!.searchMessages(searchCriteria: 'TEXT "$query"');
      } else {
        // Search all messages
        searchResult = await _client!.searchMessages(searchCriteria: 'ALL');
      }

      if (searchResult.matchingSequence?.isEmpty ?? true) {
        _logger.d('No messages found matching search criteria');
        return [];
      }

      // Fetch the matching messages
      final sequence = searchResult.matchingSequence!;
      final fetchLimit = limit != null ? 
        MessageSequence.fromIds(sequence.toList().take(limit).toList()) : 
        sequence;

      final fetchResult = await _client!.fetchMessages(
        fetchLimit,
        'BODY.PEEK[]',
      );

      List<MimeMessage> messages = fetchResult.messages;

      // Simple local filtering if query is provided
      if (query != null && query.isNotEmpty) {
        final queryLower = query.toLowerCase();
        messages = messages.where((message) {
          final subject = message.decodeSubject()?.toLowerCase() ?? '';
          final from = message.from?.toString().toLowerCase() ?? '';
          return subject.contains(queryLower) || from.contains(queryLower);
        }).toList();
      }

      // Apply limit if specified
      if (limit != null && messages.length > limit) {
        messages = messages.take(limit).toList();
      }

      _logger.i('Found ${messages.length} messages matching search criteria');
      return messages;
    } catch (e) {
      _logger.e('Failed to search messages in $mailboxName', error: e);
      return [];
    }
  }

  // =======================================================
  // = Function: getMailboxes
  // = Description: Gets list of available mailboxes
  // =======================================================
  Future<List<Mailbox>> getMailboxes() async {
    try {
      await _ensureConnected();
      _logger.d('Fetching mailbox list');

      final listResult = await _client!.listMailboxes();
      final mailboxes = listResult;
      
      _logger.i('Found ${mailboxes.length} mailboxes');
      return mailboxes;
    } catch (e, stack) {
      _logger.e('Failed to fetch mailboxes', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: getMailboxStatus
  // = Description: Gets status information for a mailbox
  // =======================================================
  Future<Mailbox?> getMailboxStatus(String mailboxName) async {
    try {
      await _ensureConnected();
      _logger.d('Getting status for mailbox: $mailboxName');

      // Try to select the mailbox to get accurate message counts
      final mailbox = await selectMailbox(mailboxName);

      if (mailbox != null) {
        _logger.d('Mailbox $mailboxName: ${mailbox.messagesExists} messages, ${mailbox.messagesUnseen} unread');
        return mailbox;
      }

      _logger.w('Mailbox $mailboxName not found or could not be selected');
      return null;
    } catch (e, stack) {
      _logger.e('Failed to get mailbox status for $mailboxName', error: e, stackTrace: stack);
      return null;
    }
  }

  // =======================================================
  // = Function: selectMailbox
  // = Description: Selects a specific mailbox for operations
  // =======================================================
  Future<Mailbox?> selectMailbox(String mailboxName) async {
    try {
      await _ensureConnected();
      _logger.d('Selecting mailbox: $mailboxName');

      // Handle INBOX specially for compatibility
      if (mailboxName.toUpperCase() == 'INBOX') {
        final mailbox = await _client!.selectInbox();
        _logger.d('Selected mailbox $mailboxName successfully');
        return mailbox;
      }

      // For other mailboxes, find the mailbox object first, then select it
      final mailboxes = await _client!.listMailboxes();
      final targetMailbox = mailboxes.where(
        (mb) => mb.name.toLowerCase() == mailboxName.toLowerCase(),
      ).firstOrNull;

      if (targetMailbox != null) {
        try {
          final selectedMailbox = await _client!.selectMailbox(targetMailbox);
          _logger.d('Selected mailbox ${targetMailbox.name} successfully: ${selectedMailbox.messagesExists} messages');
          return selectedMailbox;
        } catch (e) {
          _logger.w('Failed to select mailbox ${targetMailbox.name}: $e');
          return null;
        }
      }

      _logger.w('Mailbox $mailboxName not found');
      return null;
    } catch (e, stack) {
      _logger.e('Failed to select mailbox $mailboxName', error: e, stackTrace: stack);
      return null;
    }
  }

  // =======================================================
  // = Function: markAsRead
  // = Description: Marks a message as read
  // =======================================================
  Future<bool> markAsRead(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot mark message as read: no UID');
        return false;
      }

      await _client!.markSeen(MessageSequence.fromId(message.uid!), silent: true);
      _logger.d('Marked message ${message.uid} as read');
      return true;
    } catch (e) {
      _logger.e('Failed to mark message as read', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: markAsUnread
  // = Description: Marks a message as unread
  // =======================================================
  Future<bool> markAsUnread(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot mark message as unread: no UID');
        return false;
      }

      await _client!.markUnseen(MessageSequence.fromId(message.uid!), silent: true);
      _logger.d('Marked message ${message.uid} as unread');
      return true;
    } catch (e) {
      _logger.e('Failed to mark message as unread', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: deleteMessage
  // = Description: Deletes a message
  // =======================================================
  Future<bool> deleteMessage(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot delete message: no UID');
        return false;
      }

      await _client!.markDeleted(MessageSequence.fromId(message.uid!));
      await _client!.expunge();
      _logger.d('Deleted message ${message.uid}');
      return true;
    } catch (e) {
      _logger.e('Failed to delete message', error: e);
      return false;
    }
  }
}
