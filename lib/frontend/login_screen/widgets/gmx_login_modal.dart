/*
=======================================================
= File: gmx_login_modal.dart
= Project: LavaMail
= Description:
=   - GMX login modal for email authentication
=   - Offers a selector and then the GMX login form
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import '../../../core/gmx/auth/gmx_auth_service.dart';
import '../../../core/gmx/online_mode/gmx_online_mode.dart' as gmx_online;
import '../../../providers/user_provider.dart';
import '../../home_screen/home_screen.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';

class GmxLoginModal extends StatefulWidget {
  const GmxLoginModal({super.key});

  @override
  State<GmxLoginModal> createState() => _GmxLoginModalState();
}

class _GmxLoginModalState extends State<GmxLoginModal> {
  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: 'Sign in to GMX',
      titleIcon: Icons.email,
      content: GmxLoginForm(
        onBack: () => Navigator.of(context).pop(),
      ),
      actions: [
        LavaMailOutlinedButton(
          text: 'Cancel',
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }
}

// The GMX login form, refactored from the previous dialog
class GmxLoginForm extends StatefulWidget {
  final VoidCallback onBack;
  const GmxLoginForm({super.key, required this.onBack});

  @override
  State<GmxLoginForm> createState() => _GmxLoginFormState();
}

class _GmxLoginFormState extends State<GmxLoginForm> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _imapHostController = TextEditingController(text: 'imap.gmx.com');
  final _imapPortController = TextEditingController(text: '993');
  final _smtpHostController = TextEditingController(text: 'mail.gmx.com');
  final _smtpPortController = TextEditingController(text: '587');
  bool _isLoading = false;
  bool _useSSL = true;
  bool _showAdvancedSettings = false;
  bool _obscurePassword = true;
  bool _hasCredentialsSaved = false;
  bool _showDirectConnect = false;

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _imapHostController.dispose();
    _imapPortController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    super.dispose();
  }

  /// Loads saved GMX credentials and pre-fills the form
  Future<void> _loadSavedCredentials() async {
    try {
      final savedUser = await GmxAuthService.loadSavedUser();
      if (savedUser != null && mounted) {
        setState(() {
          _hasCredentialsSaved = true;
          _showDirectConnect = true;
          // Pre-fill email (visible) and password (hidden)
          _emailController.text = savedUser.email;
          _passwordController.text = savedUser.password;
          _imapHostController.text = savedUser.imapHost;
          _imapPortController.text = savedUser.imapPort.toString();
          _smtpHostController.text = savedUser.smtpHost;
          _smtpPortController.text = savedUser.smtpPort.toString();
          _useSSL = savedUser.useSSL;
        });
        _logger.i('GMX credentials loaded and form pre-filled for: ${savedUser.email}');
      }
    } catch (e) {
      _logger.w('Failed to load saved GMX credentials: $e');
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    await _performLogin();
  }

  /// Handles direct connection with saved credentials
  Future<void> _handleDirectConnect() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No saved credentials found'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    await _performLogin();
  }

  /// Performs the actual login process (used by both form login and direct connect)
  Future<void> _performLogin() async {
    setState(() { _isLoading = true; });
    try {
      _logger.d('Attempting GMX login');
      final user = await GmxAuthService.signInWithCredentials(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
      );
      if (user != null) {
        if (mounted) {
          final userProvider = Provider.of<UserProvider>(context, listen: false);
          userProvider.userType = UserType.gmx;
          userProvider.setGmxUser(user);

          // Start data loading process as per Mermaid diagram
          await _startDataLoading(user);

          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        }
      }
    } catch (e) {
      _logger.e('GMX login failed', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  /// Starts the data loading process as defined in the Mermaid diagram
  Future<void> _startDataLoading(dynamic user) async {
    try {
      _logger.i('Starting GMX data loading process...');

      // Create GMX online mode service instance
      final gmxService = gmx_online.GmxOnlineMode();

      // Step 1: Initialize GMX service
      _logger.d('Initializing GMX service...');
      await gmxService.initialize(user);

      // Step 2: Discover and map GMX folder structure (inspired by Python script)
      _logger.d('Discovering GMX folder structure...');
      final folderStructure = await gmxService.discoverAndMapGmxStructure();
      _logger.i('GMX structure discovered: ${folderStructure['totalFolders']} folders, ${folderStructure['totalMessages']} total messages');

      // Step 3: Load all folders with real counts
      _logger.d('Loading GMX folders with real message counts...');
      final allFolders = await gmxService.getAllFolders();
      _logger.i('Loaded ${allFolders.length} GMX folders with real counts');

      // Step 4: Analyze attachments
      _logger.d('Analyzing GMX attachments...');
      final attachmentAnalysis = await gmxService.getAttachmentAnalysis();
      _logger.i('GMX attachment analysis: ${attachmentAnalysis['totalAttachments']} attachments in ${attachmentAnalysis['emailsWithAttachments']} emails');

      // Step 5: Cache the discovered structure for quick access
      _logger.d('Caching GMX structure data...');
      // Note: In a real implementation, you might want to cache this data
      // using SharedPreferences or Hive for faster subsequent loads

      _logger.i('GMX data loading completed successfully');
      _logger.i('Summary: ${folderStructure['totalMessages']} total emails, ${folderStructure['totalUnread']} unread, ${attachmentAnalysis['totalAttachments']} attachments');

    } catch (e, stack) {
      _logger.w('Error during GMX data loading: $e', stackTrace: stack);
      // Continue to home screen even if data loading fails
      // The home screen widgets will handle the case where data is not available
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.email, color: Colors.blue, size: 32),
              const SizedBox(width: 12),
              const Text(
                'GMX Login',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Show direct connect button if credentials are saved
          if (_showDirectConnect && _hasCredentialsSaved) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Saved credentials found',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Email: ${_emailController.text}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _handleDirectConnect,
                    icon: const Icon(Icons.login),
                    label: const Text('Connect with saved credentials'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showDirectConnect = false;
                      });
                    },
                    child: const Text('Use different credentials'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show form if no direct connect or user chose different credentials
          if (!_showDirectConnect || !_hasCredentialsSaved)
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                LavaMailTextFormField(
                  label: 'Email Address',
                  hint: '<EMAIL>',
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  filled: _hasCredentialsSaved,
                  fillColor: _hasCredentialsSaved ? LavaMailTheme.primaryColor.withValues(alpha: 0.1) : null,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email address';
                    }
                    if (!value.contains('@')) {
                      return 'Please enter a valid email address';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: LavaMailTheme.spacingM),
                LavaMailTextFormField(
                  label: 'Password',
                  hint: _hasCredentialsSaved ? '••••••••••••••••' : 'Your GMX password or app-specific password',
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  filled: _hasCredentialsSaved,
                  fillColor: _hasCredentialsSaved ? LavaMailTheme.primaryColor.withValues(alpha: 0.1) : null,
                  suffixIcon: IconButton(
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                    icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your password';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: LavaMailTheme.spacingM),
                LavaMailOutlinedButton(
                  text: 'Advanced Settings',
                  icon: _showAdvancedSettings ? Icons.expand_less : Icons.expand_more,
                  onPressed: () {
                    setState(() {
                      _showAdvancedSettings = !_showAdvancedSettings;
                    });
                  },
                ),
                if (_showAdvancedSettings) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _imapHostController,
                          decoration: const InputDecoration(
                            labelText: 'IMAP Host',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 80,
                        child: TextFormField(
                          controller: _imapPortController,
                          decoration: const InputDecoration(
                            labelText: 'Port',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            final port = int.tryParse(value);
                            if (port == null || port < 1 || port > 65535) {
                              return 'Invalid';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _smtpHostController,
                          decoration: const InputDecoration(
                            labelText: 'SMTP Host',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 80,
                        child: TextFormField(
                          controller: _smtpPortController,
                          decoration: const InputDecoration(
                            labelText: 'Port',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            final port = int.tryParse(value);
                            if (port == null || port < 1 || port > 65535) {
                              return 'Invalid';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const SizedBox(height: 12),
                  const Text(
                    'SMTP Configuration:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<int>(
                          title: const Text('Port 587 (STARTTLS)'),
                          subtitle: const Text('Recommended'),
                          value: 587,
                          groupValue: int.parse(_smtpPortController.text),
                          onChanged: (value) {
                            setState(() {
                              _smtpPortController.text = value.toString();
                              _useSSL = false; // STARTTLS for port 587
                            });
                          },
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<int>(
                          title: const Text('Port 465 (SSL/TLS)'),
                          value: 465,
                          groupValue: int.parse(_smtpPortController.text),
                          onChanged: (value) {
                            setState(() {
                              _smtpPortController.text = value.toString();
                              _useSSL = true; // SSL/TLS for port 465
                            });
                          },
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: LavaMailTheme.spacingL),
                LavaMailButton(
                  text: 'Login to GMX',
                  onPressed: _handleLogin,
                  isLoading: _isLoading,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            'Important Setup Information',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '1. Enable IMAP/SMTP access in your GMX account:\n   • Log into GMX webmail\n   • Go to Settings → POP3/IMAP\n   • Enable IMAP access',
                        style: TextStyle(fontSize: 11, color: Colors.black87),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.security, color: Colors.orange.shade700, size: 14),
                                const SizedBox(width: 6),
                                Text(
                                  'If you have 2FA enabled:',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              '• Create an app-specific password in GMX Settings → Security\n• Use the app-specific password instead of your regular password',
                              style: TextStyle(fontSize: 10, color: Colors.black87),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Default settings:\n• IMAP: imap.gmx.com:993 (SSL)\n• SMTP: mail.gmx.com:587 (STARTTLS) or :465 (SSL/TLS)',
                        style: TextStyle(fontSize: 10, color: Colors.black54),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
