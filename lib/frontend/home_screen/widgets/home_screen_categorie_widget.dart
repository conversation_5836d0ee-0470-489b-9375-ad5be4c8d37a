import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import '../../theme/app_theme.dart';
import '../../../providers/user_provider.dart';
import '../../../core/gmail/offline_mode/gmail_offline_mode.dart' as gmail_offline;
import '../../../core/gmail/online_mode/gmail_online_mode.dart' as gmail_online;
import '../../../core/gmx/online_mode/gmx_online_mode.dart' as gmx_online;
import '../../../core/yahoo/online_mode/yahoo_online_mode.dart' as yahoo_online;
import '../../../core/icloud/online_mode/icloud_online_mode.dart' as icloud_online;

// =======================================================
// = File: home_screen_categorie_widget.dart
// = Description: Universal categories widget that automatically detects the email provider
//                and displays all categories with real email counts
// = Author: LavaMail Development Team
// = Created: 2025-06-21
// =======================================================

/// Data structure for email category information
class EmailCategoryData {
  final String id;
  final String name;
  final String displayName;
  final IconData icon;
  final Color color;
  final int emailCount;
  final String? size;
  final bool isLoading;
  final String? error;
  final String provider;

  EmailCategoryData({
    required this.id,
    required this.name,
    required this.displayName,
    required this.icon,
    required this.color,
    required this.emailCount,
    this.size,
    this.isLoading = false,
    this.error,
    required this.provider,
  });

  EmailCategoryData copyWith({
    String? id,
    String? name,
    String? displayName,
    IconData? icon,
    Color? color,
    int? emailCount,
    String? size,
    bool? isLoading,
    String? error,
    String? provider,
  }) {
    return EmailCategoryData(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      emailCount: emailCount ?? this.emailCount,
      size: size ?? this.size,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      provider: provider ?? this.provider,
    );
  }
}

/// Universal categories widget that automatically detects the email provider
/// and displays all categories with real email counts
class HomeScreenCategorieWidget extends StatefulWidget {
  const HomeScreenCategorieWidget({super.key});

  @override
  State<HomeScreenCategorieWidget> createState() => _HomeScreenCategorieWidgetState();
}

class _HomeScreenCategorieWidgetState extends State<HomeScreenCategorieWidget> {
  final Logger _logger = Logger();
  List<EmailCategoryData> _categories = [];
  String _currentProvider = 'Unknown';
  bool _isLoading = true;
  String? _error;
  bool _isOnlineMode = true;

  @override
  void initState() {
    super.initState();
    _detectProviderAndLoadCategories();
  }



  /// Automatically detect the current email provider and load appropriate categories
  Future<void> _detectProviderAndLoadCategories() async {
    if (!mounted) return;

    // Avoid reloading if already loaded
    if (_categories.isNotEmpty && !_isLoading) {
      _logger.d('Categories already loaded, skipping reload');
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      _logger.d('Categories Widget - UserType: ${userProvider.userType}');

      // Detect current provider and load categories
      if (userProvider.userType == UserType.gmail && userProvider.user != null) {
        if (_currentProvider != 'Gmail') {
          _currentProvider = 'Gmail';
          await _loadGmailCategories(userProvider.user!);
        }
      } else if (userProvider.userType == UserType.gmx && userProvider.gmxUser != null) {
        if (_currentProvider != 'GMX') {
          _currentProvider = 'GMX';
          await _loadGmxCategories(userProvider.gmxUser!);
        }
      } else if (userProvider.userType == UserType.yahoo && userProvider.yahooUser != null) {
        if (_currentProvider != 'Yahoo') {
          _currentProvider = 'Yahoo';
          await _loadYahooCategories(userProvider.yahooUser!);
        }
      } else if (userProvider.userType == UserType.icloud && userProvider.icloudUser != null) {
        if (_currentProvider != 'iCloud') {
          _currentProvider = 'iCloud';
          await _loadIcloudCategories(userProvider.icloudUser!);
        }
      } else {
        // Fallback: check if any user exists regardless of userType
        if (userProvider.user != null && _currentProvider != 'Gmail') {
          _currentProvider = 'Gmail';
          await _loadGmailCategories(userProvider.user!);
        } else {
          setState(() {
            _categories = [];
            _currentProvider = 'Unknown';
            _error = 'No email provider connected';
            _isLoading = false;
          });
          return;
        }
      }
    } catch (e) {
      _logger.e('Categories Widget - Error: $e');
      if (mounted) {
        setState(() {
          _categories = [];
          _error = 'Error loading categories: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// Load Gmail categories (online/offline mode)
  Future<void> _loadGmailCategories(GoogleSignInAccount user) async {
    try {
      final mode = gmail_offline.DataModeService.currentMode;
      _isOnlineMode = mode == gmail_offline.DataMode.online;
      
      _logger.d('Categories Widget - Loading Gmail categories in ${_isOnlineMode ? 'ONLINE' : 'OFFLINE'} mode');

      final categories = <EmailCategoryData>[];

      // Define Gmail categories
      final gmailCategories = [
        {'id': 'INBOX', 'name': 'Inbox', 'icon': Icons.inbox, 'color': Colors.blue},
        {'id': 'SENT', 'name': 'Sent', 'icon': Icons.send, 'color': LavaMailTheme.sentColor},
        {'id': 'SPAM', 'name': 'Spam', 'icon': Icons.report, 'color': Colors.red},
        {'id': 'CATEGORY_PERSONAL', 'name': 'Primary', 'icon': Icons.person, 'color': Colors.blue},
        {'id': 'CATEGORY_SOCIAL', 'name': 'Social', 'icon': Icons.people, 'color': Colors.green},
        {'id': 'CATEGORY_PROMOTIONS', 'name': 'Promotions', 'icon': Icons.local_offer, 'color': LavaMailTheme.promotionsColor},
        {'id': 'CATEGORY_UPDATES', 'name': 'Updates', 'icon': Icons.update, 'color': LavaMailTheme.updatesColor},
        {'id': 'CATEGORY_FORUMS', 'name': 'Forums', 'icon': Icons.forum, 'color': LavaMailTheme.forumsColor},
        {'id': 'DRAFT', 'name': 'Drafts', 'icon': Icons.drafts, 'color': Colors.orange},
        {'id': 'TRASH', 'name': 'Trash', 'icon': Icons.delete, 'color': Colors.grey},
      ];

      // Load counts for each category
      for (final category in gmailCategories) {
        try {
          final count = await _getGmailCategoryCount(user, category['id'] as String);
          categories.add(EmailCategoryData(
            id: category['id'] as String,
            name: category['id'] as String,
            displayName: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
            emailCount: count,
            provider: _currentProvider,
          ));
        } catch (e) {
          _logger.w('Error loading count for ${category['name']}: $e');
          categories.add(EmailCategoryData(
            id: category['id'] as String,
            name: category['id'] as String,
            displayName: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
            emailCount: 0,
            error: e.toString(),
            provider: _currentProvider,
          ));
        }
      }

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('Error loading Gmail categories: $e');
      if (mounted) {
        setState(() {
          _error = 'Error loading Gmail categories';
          _isLoading = false;
        });
      }
    }
  }

  /// Get Gmail category count based on current mode
  Future<int> _getGmailCategoryCount(GoogleSignInAccount user, String categoryId) async {
    try {
      if (_isOnlineMode) {
        final apiService = gmail_online.GmailApiService(user);
        switch (categoryId) {
          case 'SPAM':
            final stats = await apiService.getSpamStats();
            return stats['count'] ?? 0;
          case 'CATEGORY_PERSONAL':
            final stats = await apiService.getCategoryStats('primary');
            return stats['count'] ?? 0;
          case 'CATEGORY_SOCIAL':
            final stats = await apiService.getCategoryStats('social');
            return stats['count'] ?? 0;
          case 'CATEGORY_PROMOTIONS':
            final stats = await apiService.getCategoryStats('promotions');
            return stats['count'] ?? 0;
          case 'CATEGORY_UPDATES':
            final stats = await apiService.getCategoryStats('updates');
            return stats['count'] ?? 0;
          case 'CATEGORY_FORUMS':
            final stats = await apiService.getCategoryStats('forums');
            return stats['count'] ?? 0;
          case 'SENT':
            final stats = await apiService.getCategoryStats('sent');
            return stats['count'] ?? 0;
          default:
            // For other categories, use generic label count
            return await gmail_offline.GmailDataService.getEmailCountForLabel(user.email, categoryId);
        }
      } else {
        // Offline mode
        return await gmail_offline.GmailDataService.getEmailCountForLabel(user.email, categoryId);
      }
    } catch (e) {
      _logger.w('Error getting count for $categoryId: $e');
      return 0;
    }
  }

  /// Load GMX categories using comprehensive folder discovery (inspired by Python gmx.py)
  Future<void> _loadGmxCategories(dynamic gmxUser) async {
    try {
      _logger.d('Categories Widget - Loading GMX categories via comprehensive discovery');

      final apiService = gmx_online.GmxOnlineMode();
      await apiService.initialize(gmxUser);

      final categories = <EmailCategoryData>[];

      // Step 1: Use comprehensive folder structure discovery
      final folderStructure = await apiService.discoverAndMapGmxStructure();
      _logger.i('GMX structure discovered: ${folderStructure['totalFolders']} folders, ${folderStructure['totalMessages']} total messages');

      // Step 2: Create categories from discovered structure

      // Inbox categories
      final inboxFolders = folderStructure['inbox'] as List<Map<String, dynamic>>;
      for (final folder in inboxFolders) {
        categories.add(EmailCategoryData(
          id: 'courrier_recu',
          name: folder['name'] as String,
          displayName: 'Courrier reçu (${folder['messageCount']})',
          icon: Icons.inbox,
          color: Colors.blue,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      // Sent categories
      final sentFolders = folderStructure['sent'] as List<Map<String, dynamic>>;
      for (final folder in sentFolders) {
        categories.add(EmailCategoryData(
          id: 'envoyes',
          name: folder['name'] as String,
          displayName: 'Envoyés (${folder['messageCount']})',
          icon: Icons.send,
          color: LavaMailTheme.sentColor,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      // Spam categories
      final spamFolders = folderStructure['spam'] as List<Map<String, dynamic>>;
      for (final folder in spamFolders) {
        categories.add(EmailCategoryData(
          id: 'spam',
          name: folder['name'] as String,
          displayName: 'Spam (${folder['messageCount']})',
          icon: Icons.report,
          color: Colors.red,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      // Additional standard folders
      final trashFolders = folderStructure['trash'] as List<Map<String, dynamic>>;
      for (final folder in trashFolders) {
        categories.add(EmailCategoryData(
          id: 'corbeille',
          name: folder['name'] as String,
          displayName: 'Corbeille (${folder['messageCount']})',
          icon: Icons.delete,
          color: Colors.grey,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      final draftsFolders = folderStructure['drafts'] as List<Map<String, dynamic>>;
      for (final folder in draftsFolders) {
        categories.add(EmailCategoryData(
          id: 'brouillons',
          name: folder['name'] as String,
          displayName: 'Brouillons (${folder['messageCount']})',
          icon: Icons.drafts,
          color: Colors.orange,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      // Important folders
      final importantFolders = folderStructure['important'] as List<Map<String, dynamic>>;
      for (final folder in importantFolders) {
        categories.add(EmailCategoryData(
          id: 'important',
          name: folder['name'] as String,
          displayName: 'Important (${folder['messageCount']})',
          icon: Icons.star,
          color: Colors.amber,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      // Archive folders
      final archiveFolders = folderStructure['archive'] as List<Map<String, dynamic>>;
      for (final folder in archiveFolders) {
        categories.add(EmailCategoryData(
          id: 'archive',
          name: folder['name'] as String,
          displayName: 'Archives (${folder['messageCount']})',
          icon: Icons.archive,
          color: Colors.brown,
          emailCount: folder['messageCount'] as int? ?? 0,
          provider: _currentProvider,
        ));
      }

      // Templates folders
      final templatesFolders = folderStructure['templates'] as List<Map<String, dynamic>>;
      for (final folder in templatesFolders) {
        if ((folder['messageCount'] as int? ?? 0) > 0) {
          categories.add(EmailCategoryData(
            id: 'templates',
            name: folder['name'] as String,
            displayName: 'Modèles (${folder['messageCount']})',
            icon: Icons.description,
            color: Colors.purple,
            emailCount: folder['messageCount'] as int? ?? 0,
            provider: _currentProvider,
          ));
        }
      }

      // Notes folders
      final notesFolders = folderStructure['notes'] as List<Map<String, dynamic>>;
      for (final folder in notesFolders) {
        if ((folder['messageCount'] as int? ?? 0) > 0) {
          categories.add(EmailCategoryData(
            id: 'notes',
            name: folder['name'] as String,
            displayName: 'Notes (${folder['messageCount']})',
            icon: Icons.note,
            color: Colors.yellow,
            emailCount: folder['messageCount'] as int? ?? 0,
            provider: _currentProvider,
          ));
        }
      }

      // Custom folders (all other folders discovered)
      final customFolders = folderStructure['custom'] as List<Map<String, dynamic>>;
      for (final folder in customFolders) {
        // Only add selectable folders with messages
        if ((folder['isSelectable'] as bool? ?? false) && (folder['messageCount'] as int? ?? 0) > 0) {
          categories.add(EmailCategoryData(
            id: 'custom_${folder['name']}',
            name: folder['name'] as String,
            displayName: '${folder['displayName']} (${folder['messageCount']})',
            icon: Icons.folder,
            color: Colors.teal,
            emailCount: folder['messageCount'] as int? ?? 0,
            provider: _currentProvider,
          ));
        }
      }

      // Log comprehensive discovery results
      _logger.i('GMX comprehensive discovery completed:');
      _logger.i('- Total folders: ${folderStructure['totalFolders']}');
      _logger.i('- Total messages: ${folderStructure['totalMessages']}');
      _logger.i('- Total unread: ${folderStructure['totalUnread']}');
      _logger.i('- Categories created: ${categories.length}');

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('Error loading GMX categories: $e');
      if (mounted) {
        setState(() {
          _error = 'Error loading GMX categories';
          _isLoading = false;
        });
      }
    }
  }





  /// Load Yahoo categories using IMAP
  Future<void> _loadYahooCategories(dynamic yahooUser) async {
    try {
      _logger.d('Categories Widget - Loading Yahoo categories via IMAP');

      final categories = <EmailCategoryData>[];

      // Define Yahoo categories (standard IMAP folders)
      final yahooCategories = [
        {'id': 'INBOX', 'name': 'Inbox', 'icon': Icons.inbox, 'color': Colors.purple},
        {'id': 'SENT', 'name': 'Sent', 'icon': Icons.send, 'color': LavaMailTheme.sentColor},
        {'id': 'SPAM', 'name': 'Spam', 'icon': Icons.report, 'color': Colors.red},
        {'id': 'DRAFTS', 'name': 'Drafts', 'icon': Icons.drafts, 'color': Colors.orange},
        {'id': 'TRASH', 'name': 'Trash', 'icon': Icons.delete, 'color': Colors.grey},
      ];

      // Load counts for each category using Yahoo IMAP service
      for (final category in yahooCategories) {
        try {
          final count = await _getYahooCategoryCount(yahooUser, category['id'] as String);
          categories.add(EmailCategoryData(
            id: category['id'] as String,
            name: category['id'] as String,
            displayName: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
            emailCount: count,
            provider: _currentProvider,
          ));
        } catch (e) {
          _logger.w('Error loading count for Yahoo ${category['name']}: $e');
          categories.add(EmailCategoryData(
            id: category['id'] as String,
            name: category['id'] as String,
            displayName: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
            emailCount: 0,
            error: e.toString(),
            provider: _currentProvider,
          ));
        }
      }

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('Error loading Yahoo categories: $e');
      if (mounted) {
        setState(() {
          _error = 'Error loading Yahoo categories';
          _isLoading = false;
        });
      }
    }
  }

  /// Get Yahoo category count using IMAP
  Future<int> _getYahooCategoryCount(dynamic yahooUser, String categoryId) async {
    try {
      final apiService = yahoo_online.YahooApiService(yahooUser);
      final stats = await apiService.getCategoryStats(categoryId.toLowerCase());
      return stats['count'] ?? 0;
    } catch (e) {
      _logger.w('Error getting Yahoo count for $categoryId: $e');
      return 0;
    }
  }

  /// Load iCloud categories using IMAP
  Future<void> _loadIcloudCategories(dynamic icloudUser) async {
    try {
      _logger.d('Categories Widget - Loading iCloud categories via IMAP');

      final categories = <EmailCategoryData>[];

      // Define iCloud categories (standard IMAP folders)
      final icloudCategories = [
        {'id': 'INBOX', 'name': 'Inbox', 'icon': Icons.inbox, 'color': Colors.grey},
        {'id': 'SENT', 'name': 'Sent', 'icon': Icons.send, 'color': LavaMailTheme.sentColor},
        {'id': 'JUNK', 'name': 'Junk', 'icon': Icons.report, 'color': Colors.red}, // iCloud uses "Junk" instead of "Spam"
        {'id': 'DRAFTS', 'name': 'Drafts', 'icon': Icons.drafts, 'color': Colors.orange},
        {'id': 'TRASH', 'name': 'Trash', 'icon': Icons.delete, 'color': Colors.grey},
      ];

      // Load counts for each category using iCloud IMAP service
      for (final category in icloudCategories) {
        try {
          final count = await _getIcloudCategoryCount(icloudUser, category['id'] as String);
          categories.add(EmailCategoryData(
            id: category['id'] as String,
            name: category['id'] as String,
            displayName: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
            emailCount: count,
            provider: _currentProvider,
          ));
        } catch (e) {
          _logger.w('Error loading count for iCloud ${category['name']}: $e');
          categories.add(EmailCategoryData(
            id: category['id'] as String,
            name: category['id'] as String,
            displayName: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
            emailCount: 0,
            error: e.toString(),
            provider: _currentProvider,
          ));
        }
      }

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('Error loading iCloud categories: $e');
      if (mounted) {
        setState(() {
          _error = 'Error loading iCloud categories';
          _isLoading = false;
        });
      }
    }
  }

  /// Get iCloud category count using IMAP
  Future<int> _getIcloudCategoryCount(dynamic icloudUser, String categoryId) async {
    try {
      final apiService = icloud_online.IcloudApiService(icloudUser);
      final stats = await apiService.getCategoryStats(categoryId.toLowerCase());
      return stats['count'] ?? 0;
    } catch (e) {
      _logger.w('Error getting iCloud count for $categoryId: $e');
      return 0;
    }
  }

  /// Handle category tap - navigate to appropriate screen
  void _onCategoryTap(EmailCategoryData category) {
    // TODO: Navigate to category-specific screens when they are created
    _logger.d('Categories Widget - Tapped on ${category.displayName} (${category.provider})');

    // For now, show a snackbar indicating the navigation would happen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigation to ${category.displayName} screen - Coming soon!'),
        duration: Duration(seconds: 2),
        backgroundColor: category.color,
      ),
    );

    // TODO: Implement navigation logic like:
    // switch (category.id) {
    //   case 'INBOX':
    //     Navigator.pushNamed(context, '/inbox');
    //     break;
    //   case 'SENT':
    //     Navigator.pushNamed(context, '/sent');
    //     break;
    //   // ... other cases
    // }
  }

  /// Refresh categories data
  Future<void> _refreshCategories() async {
    await _detectProviderAndLoadCategories();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        // Trigger reload if user provider changes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _detectProviderAndLoadCategories();
          }
        });

        return Container(
          margin: EdgeInsets.all(LavaMailTheme.spacingM),
          padding: EdgeInsets.all(LavaMailTheme.spacingM),
          decoration: BoxDecoration(
            color: LavaMailTheme.surfaceColor,
            borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusL),
            boxShadow: LavaMailTheme.cardShadow,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.folder_outlined,
                    color: LavaMailTheme.primaryColor,
                    size: 24,
                  ),
                  SizedBox(width: LavaMailTheme.spacingS),
                  Text(
                    'Categories',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: LavaMailTheme.textPrimaryColor,
                    ),
                  ),
                  Spacer(),
                  if (_currentProvider != 'Unknown')
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: LavaMailTheme.spacingS,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getProviderColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _currentProvider,
                        style: TextStyle(
                          color: _getProviderColor(),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  SizedBox(width: LavaMailTheme.spacingS),
                  IconButton(
                    onPressed: _isLoading ? null : _refreshCategories,
                    icon: _isLoading
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(LavaMailTheme.primaryColor),
                            ),
                          )
                        : Icon(Icons.refresh, size: 20),
                    tooltip: 'Refresh categories',
                  ),
                ],
              ),
              SizedBox(height: LavaMailTheme.spacingM),
              if (_error != null)
                Container(
                  padding: EdgeInsets.all(LavaMailTheme.spacingS),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red, size: 20),
                      SizedBox(width: LavaMailTheme.spacingS),
                      Expanded(child: Text(_error!, style: TextStyle(color: Colors.red))),
                    ],
                  ),
                )
              else if (_isLoading)
                _buildLoadingState()
              else
                _buildCategoriesGrid(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        for (int i = 0; i < 6; i++)
          Container(
            margin: EdgeInsets.only(bottom: LavaMailTheme.spacingS),
            padding: EdgeInsets.all(LavaMailTheme.spacingM),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: LavaMailTheme.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 16,
                        width: 100,
                        decoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        height: 12,
                        width: 60,
                        decoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(LavaMailTheme.primaryColor),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCategoriesGrid() {
    if (_categories.isEmpty) {
      return Container(
        padding: EdgeInsets.all(LavaMailTheme.spacingL),
        child: Column(
          children: [
            Icon(
              Icons.folder_off,
              size: 48,
              color: LavaMailTheme.textSecondaryColor,
            ),
            SizedBox(height: LavaMailTheme.spacingM),
            Text(
              'No categories found',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: LavaMailTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: _categories.map((category) => _buildCategoryItem(category)).toList(),
    );
  }

  Widget _buildCategoryItem(EmailCategoryData category) {
    return Container(
      margin: EdgeInsets.only(bottom: LavaMailTheme.spacingS),
      child: InkWell(
        onTap: () => _onCategoryTap(category),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.all(LavaMailTheme.spacingM),
          decoration: BoxDecoration(
            color: category.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: category.color.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                category.icon,
                color: category.color,
                size: 24,
              ),
              SizedBox(width: LavaMailTheme.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.displayName,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: LavaMailTheme.textPrimaryColor,
                      ),
                    ),
                    if (category.error != null)
                      Text(
                        'Error loading',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.red,
                        ),
                      )
                    else
                      Text(
                        '${category.emailCount} emails',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: LavaMailTheme.textSecondaryColor,
                        ),
                      ),
                  ],
                ),
              ),
              if (category.isLoading)
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(category.color),
                  ),
                )
              else
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: LavaMailTheme.spacingS,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: category.color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${category.emailCount}',
                    style: TextStyle(
                      color: category.color,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              SizedBox(width: LavaMailTheme.spacingS),
              Icon(
                Icons.arrow_forward_ios,
                color: LavaMailTheme.textSecondaryColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getProviderColor() {
    switch (_currentProvider) {
      case 'Gmail':
        return Colors.red;
      case 'GMX':
        return Colors.blue;
      case 'Yahoo':
        return Colors.purple;
      case 'iCloud':
        return Colors.grey;
      default:
        return LavaMailTheme.primaryColor;
    }
  }
}
