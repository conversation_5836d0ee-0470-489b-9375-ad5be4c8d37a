import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import '../../theme/app_theme.dart';
import '../../../providers/user_provider.dart';
import '../../../core/gmail/online_mode/gmail_online_mode.dart' as gmail_online;
import '../../../core/gmail/offline_mode/gmail_offline_mode.dart' as gmail_offline;
import '../../../core/gmx/online_mode/gmx_online_mode.dart' as gmx_online;
import '../../../core/yahoo/online_mode/yahoo_online_mode.dart' as yahoo_online;
import '../../../core/icloud/online_mode/icloud_online_mode.dart' as icloud_online;



// =======================================================
// = File: home_screen_information_widget.dart
// = Description: Universal information widget that automatically detects the email provider
//                and displays total email count and attachment count for any provider
// = Author: LavaMail Development Team
// = Created: 2025-06-21
// =======================================================

/// Data structure for email information
class EmailInformationData {
  final int totalEmails;
  final bool hasAttachments;
  final int attachmentCount;
  final int? emailsWithAttachments;
  final String? error;
  final String provider;

  EmailInformationData({
    required this.totalEmails,
    required this.hasAttachments,
    required this.attachmentCount,
    this.emailsWithAttachments,
    this.error,
    required this.provider,
  });
}

/// Universal information widget that automatically detects the email provider
/// and displays real email count and attachment count
class HomeScreenInformationWidget extends StatefulWidget {
  const HomeScreenInformationWidget({super.key});

  @override
  State<HomeScreenInformationWidget> createState() => _HomeScreenInformationWidgetState();
}

class _HomeScreenInformationWidgetState extends State<HomeScreenInformationWidget>
    with TickerProviderStateMixin {
  final Logger _logger = Logger();
  bool _isOnlineMode = true;
  EmailInformationData? _data;
  String? _error;
  String _currentProvider = 'Unknown';

  // Animation controllers for numbers
  late AnimationController _emailCountController;
  late AnimationController _attachmentCountController;
  late Animation<int> _emailCountAnimation;
  late Animation<int> _attachmentCountAnimation;

  // Final values for animations
  int _finalEmailCount = 0;
  int _finalAttachmentCount = 0;

  // Real-time counters
  int _currentEmailCount = 0;
  int _currentAttachmentCount = 0;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _emailCountController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _attachmentCountController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // Initialize animations with default values
    _emailCountAnimation = IntTween(begin: 0, end: 0).animate(
      CurvedAnimation(parent: _emailCountController, curve: Curves.easeOutCubic),
    );
    _attachmentCountAnimation = IntTween(begin: 0, end: 0).animate(
      CurvedAnimation(parent: _attachmentCountController, curve: Curves.easeOutCubic),
    );

    _detectProviderAndLoadData();
  }

  @override
  void dispose() {
    _emailCountController.dispose();
    _attachmentCountController.dispose();
    super.dispose();
  }

  /// Automatically detect the current email provider and load appropriate data
  Future<void> _detectProviderAndLoadData() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Detect current provider
      if (userProvider.userType == UserType.gmail && userProvider.user != null) {
        _currentProvider = 'Gmail';
        await _loadGmailData(userProvider.user!);
      } else if (userProvider.userType == UserType.gmx && userProvider.gmxUser != null) {
        _currentProvider = 'GMX';
        await _loadGmxData(userProvider.gmxUser!);
      } else if (userProvider.userType == UserType.yahoo && userProvider.yahooUser != null) {
        _currentProvider = 'Yahoo';
        await _loadYahooData(userProvider.yahooUser!);
      } else if (userProvider.userType == UserType.icloud && userProvider.icloudUser != null) {
        _currentProvider = 'iCloud';
        await _loadIcloudData(userProvider.icloudUser!);
      } else {
        _logger.w('No valid user provider found');
        setState(() {
          _error = 'Aucun fournisseur de messagerie détecté';
        });
      }
    } catch (e) {
      _logger.e('Error detecting provider: $e');
      setState(() {
        _error = 'Erreur lors de la détection du fournisseur';
      });
    }
  }

  /// Load Gmail data (online/offline mode)
  Future<void> _loadGmailData(GoogleSignInAccount user) async {
    try {
      final mode = gmail_offline.DataModeService.currentMode;
      _isOnlineMode = mode == gmail_offline.DataMode.online;

      if (_isOnlineMode) {
        await _loadGmailOnlineData(user);
      } else {
        await _loadGmailOfflineData(user);
      }
    } catch (e) {
      _logger.e('Error loading Gmail data: $e');
      setState(() {
        _error = 'Erreur lors du chargement des données Gmail';
      });
    }
  }

  /// Load Gmail data from online API
  Future<void> _loadGmailOnlineData(GoogleSignInAccount user) async {
    final apiService = gmail_online.GmailApiService(user);
    
    // Get total email count
    final totalEmails = await apiService.getAllEmailsCountWithProgress(
      onProgress: (current, total) => _updateEmailCountProgress(current, total),
    );

    // Get attachment information
    final attachmentInfo = await apiService.getTotalAttachmentsCountWithProgress(
      onProgress: (attachments, emails) => _updateAttachmentCountProgress(attachments, emails),
    );

    if (mounted) {
      setState(() {
        _data = EmailInformationData(
          totalEmails: totalEmails,
          hasAttachments: attachmentInfo['hasAttachments'] ?? false,
          attachmentCount: attachmentInfo['totalAttachments'] ?? 0,
          emailsWithAttachments: attachmentInfo['emailsWithAttachments'] ?? 0,
          provider: _currentProvider,
        );
        // Update animation targets
        _finalEmailCount = totalEmails;
        _finalAttachmentCount = attachmentInfo['totalAttachments'] ?? 0;
      });
      _startAnimations();
    }
  }

  /// Load Gmail data from offline storage
  Future<void> _loadGmailOfflineData(GoogleSignInAccount user) async {
    final totalEmails = await gmail_offline.GmailDataService.getTotalEmails(user.email);
    final attachmentInfo = await gmail_offline.GmailDataService.getTotalAttachmentsCount(user.email);

    if (mounted) {
      setState(() {
        _data = EmailInformationData(
          totalEmails: totalEmails,
          hasAttachments: attachmentInfo['hasAttachments'] ?? false,
          attachmentCount: attachmentInfo['totalAttachments'] ?? 0,
          emailsWithAttachments: attachmentInfo['emailsWithAttachments'] ?? 0,
          provider: _currentProvider,
        );
        // Update animation targets
        _finalEmailCount = totalEmails;
        _finalAttachmentCount = attachmentInfo['totalAttachments'] ?? 0;
      });
      _startAnimations();
    }
  }

  /// Load GMX data using comprehensive folder discovery (inspired by Python gmx.py)
  Future<void> _loadGmxData(dynamic gmxUser) async {
    try {
      _logger.d('Information Widget - Loading GMX data via comprehensive discovery');

      final apiService = gmx_online.GmxOnlineMode();
      await apiService.initialize(gmxUser);

      // Step 1: Use comprehensive folder structure discovery
      final folderStructure = await apiService.discoverAndMapGmxStructure();
      _logger.i('GMX structure discovered for information widget: ${folderStructure['totalFolders']} folders');

      // Step 2: Get total email count from discovered structure
      final totalEmails = folderStructure['totalMessages'] as int? ?? 0;

      // Update email count with progress animation
      _updateEmailCountProgress(totalEmails, totalEmails);

      // Step 3: Get comprehensive attachment analysis
      final attachmentInfo = await apiService.getAttachmentAnalysis(maxSample: 200);

      // Update attachment count with progress animation
      _updateAttachmentCountProgress(
        attachmentInfo['totalAttachments'] ?? 0,
        attachmentInfo['emailsWithAttachments'] ?? 0
      );

      if (mounted) {
        setState(() {
          _data = EmailInformationData(
            totalEmails: totalEmails,
            hasAttachments: attachmentInfo['hasAttachments'] ?? false,
            attachmentCount: attachmentInfo['totalAttachments'] ?? 0,
            emailsWithAttachments: attachmentInfo['emailsWithAttachments'] ?? 0,
            provider: _currentProvider,
          );
          // Update animation targets
          _finalEmailCount = totalEmails;
          _finalAttachmentCount = attachmentInfo['totalAttachments'] ?? 0;
        });
        _startAnimations();
      }

      _logger.i('GMX comprehensive data loaded:');
      _logger.i('- Total emails (all folders): $totalEmails');
      _logger.i('- Total unread: ${folderStructure['totalUnread']}');
      _logger.i('- Total attachments: ${attachmentInfo['totalAttachments']}');
      _logger.i('- Emails with attachments: ${attachmentInfo['emailsWithAttachments']}');
      _logger.i('- Sample size analyzed: ${attachmentInfo['sampleSize']}');

    } catch (e) {
      _logger.e('Error loading GMX comprehensive data: $e');
      if (mounted) {
        setState(() {
          _error = 'Erreur lors du chargement des données GMX';
        });
      }
    }
  }

  /// Load Yahoo data using IMAP
  Future<void> _loadYahooData(dynamic yahooUser) async {
    try {
      _logger.d('Information Widget - Loading Yahoo data via IMAP');

      final apiService = yahoo_online.YahooApiService(yahooUser);

      // Get total email count from INBOX (main folder)
      final inboxStats = await apiService.getCategoryStats('inbox');
      final totalEmails = inboxStats['count'] ?? 0;

      // For IMAP providers, attachment analysis is complex and resource-intensive
      // For now, we'll set attachments to false but this could be implemented later

      // Get attachment analysis from Yahoo IMAP service
      final attachmentInfo = await apiService.getAttachmentAnalysis(maxSample: 100);

      if (mounted) {
        setState(() {
          _data = EmailInformationData(
            totalEmails: totalEmails,
            hasAttachments: attachmentInfo['hasAttachments'] ?? false,
            attachmentCount: attachmentInfo['totalAttachments'] ?? 0,
            emailsWithAttachments: attachmentInfo['emailsWithAttachments'] ?? 0,
            provider: _currentProvider,
          );
          // Update animation targets
          _finalEmailCount = totalEmails;
          _finalAttachmentCount = attachmentInfo['totalAttachments'] ?? 0;
        });
        _startAnimations();
      }
    } catch (e) {
      _logger.e('Error loading Yahoo data: $e');
      if (mounted) {
        setState(() {
          _error = 'Erreur lors du chargement des données Yahoo';
        });
      }
    }
  }

  /// Load iCloud data using IMAP
  Future<void> _loadIcloudData(dynamic icloudUser) async {
    try {
      _logger.d('Information Widget - Loading iCloud data via IMAP');

      final apiService = icloud_online.IcloudApiService(icloudUser);

      // Get total email count from INBOX (main folder)
      final inboxStats = await apiService.getCategoryStats('inbox');
      final totalEmails = inboxStats['count'] ?? 0;

      // For IMAP providers, attachment analysis is complex and resource-intensive
      // For now, we'll set attachments to false but this could be implemented later

      // Get attachment analysis from iCloud IMAP service
      final attachmentInfo = await apiService.getAttachmentAnalysis(maxSample: 100);

      if (mounted) {
        setState(() {
          _data = EmailInformationData(
            totalEmails: totalEmails,
            hasAttachments: attachmentInfo['hasAttachments'] ?? false,
            attachmentCount: attachmentInfo['totalAttachments'] ?? 0,
            emailsWithAttachments: attachmentInfo['emailsWithAttachments'] ?? 0,
            provider: _currentProvider,
          );
          // Update animation targets
          _finalEmailCount = totalEmails;
          _finalAttachmentCount = attachmentInfo['totalAttachments'] ?? 0;
        });
        _startAnimations();
      }
    } catch (e) {
      _logger.e('Error loading iCloud data: $e');
      if (mounted) {
        setState(() {
          _error = 'Erreur lors du chargement des données iCloud';
        });
      }
    }
  }

  void _startAnimations() {
    _emailCountAnimation = IntTween(
      begin: 0,
      end: _finalEmailCount,
    ).animate(CurvedAnimation(
      parent: _emailCountController,
      curve: Curves.easeOutCubic,
    ));

    _attachmentCountAnimation = IntTween(
      begin: 0,
      end: _finalAttachmentCount,
    ).animate(CurvedAnimation(
      parent: _attachmentCountController,
      curve: Curves.easeOutCubic,
    ));

    _emailCountController.reset();
    _attachmentCountController.reset();

    _emailCountController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _attachmentCountController.forward();
      }
    });
  }

  void _updateEmailCountProgress(int currentCount, int totalCount) {
    if (!mounted) return;
    setState(() {
      _currentEmailCount = currentCount;
      _finalEmailCount = totalCount;
    });
  }

  void _updateAttachmentCountProgress(int currentAttachments, int currentEmailsWithAttachments) {
    if (!mounted) return;
    setState(() {
      _currentAttachmentCount = currentAttachments;
      _finalAttachmentCount = currentAttachments;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(LavaMailTheme.spacingM),
      padding: EdgeInsets.all(LavaMailTheme.spacingM),
      decoration: BoxDecoration(
        color: LavaMailTheme.surfaceColor,
        borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusL),
        boxShadow: LavaMailTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: LavaMailTheme.primaryColor,
                size: 24,
              ),
              SizedBox(width: LavaMailTheme.spacingS),
              Text(
                'Informations',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: LavaMailTheme.textPrimaryColor,
                ),
              ),
              Spacer(),
              if (_currentProvider != 'Unknown')
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: LavaMailTheme.spacingS,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _currentProvider,
                    style: TextStyle(
                      color: LavaMailTheme.primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: LavaMailTheme.spacingM),
          if (_error != null)
            Container(
              padding: EdgeInsets.all(LavaMailTheme.spacingS),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error, color: Colors.red, size: 20),
                  SizedBox(width: LavaMailTheme.spacingS),
                  Expanded(child: Text(_error!, style: TextStyle(color: Colors.red))),
                ],
              ),
            )
          else
            _buildInformationContent(),
        ],
      ),
    );
  }

  Widget _buildInformationContent() {
    final data = _data;
    final hasData = data != null;

    return Column(
      children: [
        // Total email count with animation
        Container(
          padding: EdgeInsets.all(LavaMailTheme.spacingS),
          decoration: BoxDecoration(
            color: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.email,
                color: LavaMailTheme.primaryColor,
                size: 24,
              ),
              SizedBox(width: LavaMailTheme.spacingS),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total des emails',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _emailCountAnimation,
                      builder: (context, child) {
                        // Show real data if available, otherwise show animation
                        final currentValue = hasData ? data.totalEmails :
                                           (_currentEmailCount > 0 ? _currentEmailCount : _emailCountAnimation.value);
                        return Text(
                          '$currentValue emails',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: LavaMailTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: LavaMailTheme.spacingS),
        // Attachment information with animation
        Container(
          padding: EdgeInsets.all(LavaMailTheme.spacingS),
          decoration: BoxDecoration(
            color: (hasData && data.hasAttachments)
                ? Colors.orange.withValues(alpha: 0.1)
                : Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                (hasData && data.hasAttachments) ? Icons.attach_file : Icons.check_circle,
                color: (hasData && data.hasAttachments) ? Colors.orange : Colors.green,
                size: 24,
              ),
              SizedBox(width: LavaMailTheme.spacingS),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pièces jointes',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _attachmentCountAnimation,
                      builder: (context, child) {
                        // Show real data if available, otherwise show animation
                        final currentAttachmentCount = hasData ? data.attachmentCount :
                                                     (_currentAttachmentCount > 0 ? _currentAttachmentCount : _attachmentCountAnimation.value);
                        return Text(
                          hasData && data.hasAttachments
                              ? '$currentAttachmentCount pièces jointes'
                              : 'Aucune pièce jointe',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: (hasData && data.hasAttachments) ? Colors.orange : Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
